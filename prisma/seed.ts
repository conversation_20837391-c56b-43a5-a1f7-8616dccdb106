import { PrismaClient } from "../src/generated/prisma";

const prisma = new PrismaClient();

async function main() {
  // Clean up existing data
  await prisma.ivf_scores.deleteMany({});
  await prisma.user_roles.deleteMany({});
  await prisma.permissions.deleteMany({});
  await prisma.profiles.deleteMany({});
  await prisma.roles.deleteMany({});

  // Seed roles
  await prisma.roles.createMany({
    data: [
      { id: 1, name: "Admin", description: "Administrator role" },
      { id: 2, name: "User", description: "Regular user role" },
      { id: 3, name: "Guest", description: "Guest role" },
    ],
    skipDuplicates: true,
  });

  const aliceAuthId = "11111111-1111-1111-1111-111111111111";
  const bobAuthId = "22222222-2222-2222-2222-222222222222";
  const carolAuthId = "33333333-3333-3333-3333-333333333333";

  // Seed profiles
  await prisma.profiles.createMany({
    data: [
      {
        auth_id: aliceAuthId,
        username: "alice",
        email: "<EMAIL>",
        display_name: "Alice",
        phone: "1234567890",
        address: "123 Main St",
      },
      {
        auth_id: bobAuthId,
        username: "bob",
        email: "<EMAIL>",
        display_name: "Bob",
        phone: "2345678901",
        address: "456 Elm St",
      },
      {
        auth_id: carolAuthId,
        username: "carol",
        email: "<EMAIL>",
        display_name: "Carol",
        phone: "3456789012",
        address: "789 Oak St",
      },
    ],
  });

  // Seed permissions (assign to role ids 1, 2, 3)
  await prisma.permissions.createMany({
    data: [
      {
        role_id: 1,
        resource: "user",
        condition: null,
        can_create: true,
        can_read: true,
        can_update: true,
        can_delete: true,
      },
      {
        role_id: 2,
        resource: "profile",
        condition: null,
        can_create: false,
        can_read: true,
        can_update: true,
        can_delete: false,
      },
      {
        role_id: 3,
        resource: "public",
        condition: null,
        can_create: false,
        can_read: true,
        can_update: false,
        can_delete: false,
      },
    ],
  });

  // Seed user_roles (assign users to roles)
  await prisma.user_roles.createMany({
    data: [
      { auth_id: aliceAuthId, role_id: 1 },
      { auth_id: bobAuthId, role_id: 2 },
      { auth_id: carolAuthId, role_id: 3 },
    ],
  });

  // Seed IVF Scores
  await prisma.ivf_scores.createMany({
    data: [
      {
        user_id: aliceAuthId,
        age: 35,
        height: 165,
        weight: 60,
        menstrual_regularity: "regular",
        infertility_duration: "over_12_months",
        ivf_attempts: 2,
        known_conditions: "PCOS",
        stress_level: 7,
        diet_type: "balanced",
        exercise_frequency: "two_to_three_times",
        sleep_quality: 6,
        emotional_support_at_home: true,
        smoking_or_alcohol_habits: "None",
        household_income_range: "from_50k_to_1l",
        living_area: "urban",
        work_stress_level: "high",
        pollution_exposure: "moderate",
        occupation_type: "desk_job",
      },
      {
        user_id: bobAuthId,
        age: 32,
        height: 170,
        weight: 70,
        menstrual_regularity: "irregular",
        infertility_duration: "six_to_twelve_months",
        ivf_attempts: 1,
        known_conditions: "Endometriosis",
        stress_level: 5,
        diet_type: "vegetarian",
        exercise_frequency: "daily",
        sleep_quality: 8,
        emotional_support_at_home: true,
        smoking_or_alcohol_habits: "Alcohol",
        household_income_range: "above_1l",
        living_area: "semi_urban",
        work_stress_level: "medium",
        pollution_exposure: "low",
        occupation_type: "field_work",
      },
    ],
  });
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
