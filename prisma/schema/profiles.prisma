model profiles {
  id                 BigInt              @id @default(autoincrement())
  created_at         DateTime            @default(now()) @db.Timestamptz(6)
  username           String?
  email              String?
  display_name       String?
  phone              String?
  address            String?
  auth_id            String?             @unique @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  updated_at         DateTime            @default(now()) @db.Timestamptz(6)
  ivf_scores         ivf_scores?
}