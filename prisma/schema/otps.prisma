model otps {
  id           BigInt   @id @default(autoincrement())
  created_at   DateTime @default(now()) @db.Timestamptz(6)
  updated_at   DateTime @default(now()) @updatedAt @db.Timestamptz(6)
  phone_number String
  code         String
  expires_at   DateTime @db.Timestamptz(6)
  attempts     Int      @default(0)
  is_verified  <PERSON>olean  @default(false)
  
  @@unique([phone_number])
  @@index([phone_number, expires_at])
}