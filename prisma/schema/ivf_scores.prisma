model ivf_scores {
  id                          String   @id @default(uuid())
  user_id                     String   @unique @db.Uuid
  user                        profiles @relation(fields: [user_id], references: [auth_id], onDelete: Cascade, onUpdate: NoAction)

  // Step 1: Biological Factors
  age                         Int
  height                      Float
  weight                      Float
  menstrual_regularity        menstrual_regularity
  infertility_duration        infertility_duration
  ivf_attempts                Int
  known_conditions            String   // Comma-separated string or normalized relation

  // Step 2: Lifestyle & Psychosocial
  stress_level                Int      // Scale: 0–10
  diet_type                   diet_type
  exercise_frequency          exercise_frequency
  sleep_quality               Int      // Scale: 0–10
  emotional_support_at_home   Boolean
  smoking_or_alcohol_habits   String   // e.g., "Smoking", "Alcohol", or both

  // Step 3: Environmental & Socioeconomic Factors
  household_income_range      income_range
  living_area                 living_area
  work_stress_level           stress_level_enum
  pollution_exposure          pollution_exposure
  occupation_type             occupation_type

  // Track user's current step in the IVF score process
  current_step                Int      @default(1) // 1: Step 1, 2: Step 2, 3: Step 3

  created_at                  DateTime @default(now())
  updated_at                  DateTime @updatedAt
}


enum menstrual_regularity {
  regular
  irregular
}

enum infertility_duration {
  under_6_months
  six_to_twelve_months
  over_12_months
}

enum diet_type {
  balanced
  vegetarian
  junk_heavy
  skipping_meals
}

enum exercise_frequency {
  daily
  two_to_three_times
  rarely_or_never
}

enum income_range {
  under_10k
  from_10k_to_50k
  from_50k_to_1l
  above_1l
}

enum living_area {
  urban
  semi_urban
  rural
}

enum stress_level_enum {
  low
  medium
  high
}

enum pollution_exposure {
  high
  moderate
  low
}

enum occupation_type {
  desk_job
  field_work
  night_shift
  homemaker
}