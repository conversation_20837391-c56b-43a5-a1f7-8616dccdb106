model guest_sessions {
  id            String   @id @default(uuid())
  session_token String   @unique
  ivf_data      Json     // Store the IVF form data as JSON
  current_step  Int      @default(1)
  email         String?
  is_verified   <PERSON><PERSON><PERSON>  @default(false)
  expires_at    DateTime
  created_at    DateTime @default(now())
  updated_at    DateTime @updatedAt

  @@index([session_token])
  @@index([expires_at])
  @@index([email])
}
