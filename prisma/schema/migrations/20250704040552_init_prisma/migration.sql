-- CreateEnum
CREATE TYPE "menstrual_regularity" AS ENUM ('regular', 'irregular');

-- CreateEnum
CREATE TYPE "infertility_duration" AS ENUM ('under_6_months', 'six_to_twelve_months', 'over_12_months');

-- CreateEnum
CREATE TYPE "diet_type" AS ENUM ('balanced', 'vegetarian', 'junk_heavy', 'skipping_meals');

-- CreateEnum
CREATE TYPE "exercise_frequency" AS ENUM ('daily', 'two_to_three_times', 'rarely_or_never');

-- CreateEnum
CREATE TYPE "income_range" AS ENUM ('under_10k', 'from_10k_to_50k', 'from_50k_to_1l', 'above_1l');

-- CreateEnum
CREATE TYPE "living_area" AS ENUM ('urban', 'semi_urban', 'rural');

-- CreateEnum
CREATE TYPE "stress_level_enum" AS ENUM ('low', 'medium', 'high');

-- CreateEnum
CREATE TYPE "pollution_exposure" AS ENUM ('high', 'moderate', 'low');

-- CreateEnum
CREATE TYPE "occupation_type" AS ENUM ('desk_job', 'field_work', 'night_shift', 'homemaker');

-- CreateTable
CREATE TABLE "ivf_scores" (
    "id" TEXT NOT NULL,
    "user_id" UUID NOT NULL,
    "age" INTEGER NOT NULL,
    "height" DOUBLE PRECISION NOT NULL,
    "weight" DOUBLE PRECISION NOT NULL,
    "menstrual_regularity" "menstrual_regularity" NOT NULL,
    "infertility_duration" "infertility_duration" NOT NULL,
    "ivf_attempts" INTEGER NOT NULL,
    "known_conditions" TEXT NOT NULL,
    "stress_level" INTEGER NOT NULL,
    "diet_type" "diet_type" NOT NULL,
    "exercise_frequency" "exercise_frequency" NOT NULL,
    "sleep_quality" INTEGER NOT NULL,
    "emotional_support_at_home" BOOLEAN NOT NULL,
    "smoking_or_alcohol_habits" TEXT NOT NULL,
    "household_income_range" "income_range" NOT NULL,
    "living_area" "living_area" NOT NULL,
    "work_stress_level" "stress_level_enum" NOT NULL,
    "pollution_exposure" "pollution_exposure" NOT NULL,
    "occupation_type" "occupation_type" NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ivf_scores_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "otps" (
    "id" BIGSERIAL NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "phone_number" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "expires_at" TIMESTAMPTZ(6) NOT NULL,
    "attempts" INTEGER NOT NULL DEFAULT 0,
    "is_verified" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "otps_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "permissions" (
    "id" BIGSERIAL NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "role_id" BIGINT NOT NULL,
    "resource" TEXT,
    "condition" TEXT,
    "can_create" BOOLEAN NOT NULL DEFAULT false,
    "can_read" BOOLEAN NOT NULL DEFAULT false,
    "can_update" BOOLEAN NOT NULL DEFAULT false,
    "can_delete" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "user_permissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "profiles" (
    "id" BIGSERIAL NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "username" TEXT,
    "email" TEXT,
    "display_name" TEXT,
    "phone" TEXT,
    "address" TEXT,
    "auth_id" UUID DEFAULT gen_random_uuid(),
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "profiles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "roles" (
    "id" BIGSERIAL NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "name" TEXT,
    "description" TEXT,

    CONSTRAINT "roles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_roles" (
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "auth_id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "role_id" BIGINT NOT NULL DEFAULT 4,

    CONSTRAINT "user_roles_pkey" PRIMARY KEY ("auth_id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ivf_scores_user_id_key" ON "ivf_scores"("user_id");

-- CreateIndex
CREATE INDEX "otps_phone_number_expires_at_idx" ON "otps"("phone_number", "expires_at");

-- CreateIndex
CREATE UNIQUE INDEX "otps_phone_number_key" ON "otps"("phone_number");

-- CreateIndex
CREATE UNIQUE INDEX "profiles_auth_id_key" ON "profiles"("auth_id");

-- AddForeignKey
ALTER TABLE "ivf_scores" ADD CONSTRAINT "ivf_scores_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("auth_id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "permissions" ADD CONSTRAINT "permissions_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
