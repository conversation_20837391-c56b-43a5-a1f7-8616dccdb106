-- CreateTable
CREATE TABLE "guest_sessions" (
    "id" TEXT NOT NULL,
    "session_token" TEXT NOT NULL,
    "ivf_data" JSONB NOT NULL,
    "current_step" INTEGER NOT NULL DEFAULT 1,
    "email" TEXT,
    "is_verified" BOOLEAN NOT NULL DEFAULT false,
    "expires_at" TIMESTAMP(3) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "guest_sessions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "guest_sessions_session_token_key" ON "guest_sessions"("session_token");

-- CreateIndex
CREATE INDEX "guest_sessions_session_token_idx" ON "guest_sessions"("session_token");

-- CreateIndex
CREATE INDEX "guest_sessions_expires_at_idx" ON "guest_sessions"("expires_at");

-- CreateIndex
CREATE INDEX "guest_sessions_email_idx" ON "guest_sessions"("email");
