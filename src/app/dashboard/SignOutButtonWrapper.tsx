"use client";

import { useRouter } from "next/navigation";
import { createClient } from "@/utils/supabase/client";
import SignOutButton from "@/components/shared/SignOutButton";

export default function SignOutButtonWrapper() {
  const router = useRouter();

  const handleSignOut = async () => {
    const supabase = createClient();
    await supabase.auth.signOut();
    router.push("/login");
    router.refresh();
  };

  return <SignOutButton onSignOut={handleSignOut} />;
}
