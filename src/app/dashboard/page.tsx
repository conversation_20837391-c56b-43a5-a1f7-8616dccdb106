import SignOutButtonWrapper from "./SignOutButtonWrapper";
import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";

export default async function DashboardPage() {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    redirect("/login");
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold">Dashboard</h1>
            </div>
            <div className="flex items-center">
              <SignOutButtonWrapper />
            </div>
          </div>
        </div>
      </nav>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Welcome to your Dashboard!
              </h3>

              <div className="space-y-3">
                <div>
                  <span className="font-medium text-gray-700">Email: </span>
                  <span className="text-gray-900">{user.email}</span>
                </div>

                {user.user_metadata?.first_name && (
                  <div>
                    <span className="font-medium text-gray-700">Name: </span>
                    <span className="text-gray-900">
                      {user.user_metadata.first_name}{" "}
                      {user.user_metadata.last_name}
                    </span>
                  </div>
                )}

                <div>
                  <span className="font-medium text-gray-700">User ID: </span>
                  <span className="text-gray-900 font-mono text-sm">
                    {user.id}
                  </span>
                </div>

                <div>
                  <span className="font-medium text-gray-700">
                    Email Confirmed:{" "}
                  </span>
                  <span
                    className={`${user.email_confirmed_at ? "text-green-600" : "text-red-600"}`}
                  >
                    {user.email_confirmed_at ? "Yes" : "No"}
                  </span>
                </div>

                <div>
                  <span className="font-medium text-gray-700">Created: </span>
                  <span className="text-gray-900">
                    {new Date(user.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
