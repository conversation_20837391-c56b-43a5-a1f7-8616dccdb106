"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { createClient } from "@/utils/supabase/client";
import LoginPage from "@/components/LoginPage";

export default function LoginPageContainer() {
  /* eslint-disable @typescript-eslint/no-unused-vars */
  const [loading, setLoading] = useState(false);
  /* eslint-disable @typescript-eslint/no-unused-vars */
  const [error, setError] = useState("");
  const router = useRouter();

  const handleSignIn = async (email: string, password: string) => {
    setLoading(true);
    setError("");

    try {
      const supabase = createClient();
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        setError(error.message);
      } else {
        router.push("/dashboard");
        router.refresh();
      }
    } catch (err) {
      setError("An unexpected error occurred");
      console.error("Login error:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = () => {
    router.push("/forgot-password");
  };

  const handleSignUp = () => {
    router.push("/signup");
  };

  return (
    <LoginPage
      onLogin={handleSignIn}
      onForgotPassword={handleForgotPassword}
      onSignUp={handleSignUp}
    />
  );
}
