import { NextRequest } from "next/server";
import { authenticate } from "@/utils/api/authenticate";
import { apiResponse } from "@/utils/api/apiResponse";
import { PrismaClient } from "@/generated/prisma";
import { getGuestSessionToken, getGuestSession, verifyGuestEmail, convertGuestToUser } from "@/utils/api/guestSession";
import { z } from "zod";

const prisma = new PrismaClient();

const guestResultsSchema = z.object({
  guestSessionToken: z.string().min(1, "Guest session token is required"),
  email: z.string().email("Valid email is required"),
});

/**
 * GET /api/v1/ivf-scores/results
 * Get IVF score results - requires authentication OR verified guest session
 */
export async function GET(req: NextRequest) {
  try {
    // First try to authenticate the user
    const authResult = await authenticate(req);
    
    if (!authResult.error) {
      // User is authenticated - get their IVF scores
      const ivfScores = await prisma.ivf_scores.findUnique({
        where: { user_id: authResult.user.id },
      });

      if (!ivfScores) {
        return apiResponse(404, "IVF scores not found for this user");
      }

      if (ivfScores.current_step < 3) {
        return apiResponse(400, "All three steps must be completed to view results");
      }

      // Calculate IVF score (simplified calculation for demo)
      const score = calculateIVFScore(ivfScores);

      return apiResponse(200, undefined, {
        ivfScores,
        calculatedScore: score,
        isAuthenticated: true,
        completedAt: ivfScores.updated_at
      });
    }

    // User is not authenticated - check for verified guest session
    const url = new URL(req.url);
    const guestSessionToken = url.searchParams.get("guestSessionToken") || getGuestSessionToken(req);
    const email = url.searchParams.get("email");

    if (!guestSessionToken || !email) {
      return apiResponse(401, "Authentication required or provide verified guest session token and email");
    }

    const guestSession = await getGuestSession(guestSessionToken);
    
    if (!guestSession) {
      return apiResponse(404, "Guest session not found or expired");
    }

    if (guestSession.email !== email) {
      return apiResponse(400, "Email does not match guest session");
    }

    if (!guestSession.is_verified) {
      return apiResponse(403, "Email verification required to view results");
    }

    if (guestSession.current_step < 3) {
      return apiResponse(400, "All three steps must be completed to view results");
    }

    // Calculate IVF score for guest
    const score = calculateIVFScore(guestSession.ivf_data);

    return apiResponse(200, undefined, {
      ivfScores: {
        ...guestSession.ivf_data,
        current_step: guestSession.current_step,
        id: guestSession.id,
        created_at: guestSession.created_at,
        updated_at: guestSession.updated_at,
      },
      calculatedScore: score,
      isAuthenticated: false,
      isGuest: true,
      completedAt: guestSession.updated_at,
      message: "Results available for verified guest session"
    });
  } catch (error) {
    console.error("GET results error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * POST /api/v1/ivf-scores/results
 * Convert verified guest session to authenticated user account
 */
export async function POST(req: NextRequest) {
  try {
    // This endpoint requires authentication to create the user account
    const { user, error } = await authenticate(req);
    if (error) return error;

    const body = await req.json();
    const result = guestResultsSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    const { guestSessionToken, email } = result.data;

    // Verify guest session and convert to user data
    const success = await convertGuestToUser(guestSessionToken, user.id);

    if (!success) {
      return apiResponse(400, "Failed to convert guest session. Session may not exist, be unverified, or email may not match.");
    }

    // Get the newly created IVF scores
    const ivfScores = await prisma.ivf_scores.findUnique({
      where: { user_id: user.id },
    });

    if (!ivfScores) {
      return apiResponse(500, "Failed to retrieve converted IVF scores");
    }

    const score = calculateIVFScore(ivfScores);

    return apiResponse(200, "Guest data successfully converted to user account", {
      ivfScores,
      calculatedScore: score,
      isAuthenticated: true,
      convertedFromGuest: true,
      completedAt: ivfScores.updated_at
    });
  } catch (error) {
    console.error("POST results conversion error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * Calculate IVF score based on the provided data
 * This is a simplified calculation for demonstration purposes
 */
function calculateIVFScore(data: any): {
  totalScore: number;
  maxScore: number;
  percentage: number;
  category: string;
  factors: {
    biological: number;
    lifestyle: number;
    environmental: number;
  };
} {
  let biologicalScore = 0;
  let lifestyleScore = 0;
  let environmentalScore = 0;

  // Biological factors (max 40 points)
  // Age scoring (younger is better for IVF)
  if (data.age <= 30) biologicalScore += 15;
  else if (data.age <= 35) biologicalScore += 12;
  else if (data.age <= 40) biologicalScore += 8;
  else biologicalScore += 4;

  // BMI calculation and scoring
  const bmi = data.weight / ((data.height / 100) ** 2);
  if (bmi >= 18.5 && bmi <= 24.9) biologicalScore += 10;
  else if (bmi >= 25 && bmi <= 29.9) biologicalScore += 7;
  else biologicalScore += 4;

  // Menstrual regularity
  if (data.menstrual_regularity === "regular") biologicalScore += 8;
  else biologicalScore += 4;

  // IVF attempts (fewer is better)
  if (data.ivf_attempts === 0) biologicalScore += 7;
  else if (data.ivf_attempts <= 2) biologicalScore += 5;
  else biologicalScore += 2;

  // Lifestyle factors (max 30 points)
  // Stress level (lower is better)
  lifestyleScore += Math.max(0, 10 - data.stress_level);

  // Diet type
  if (data.diet_type === "balanced") lifestyleScore += 8;
  else if (data.diet_type === "vegetarian") lifestyleScore += 6;
  else lifestyleScore += 3;

  // Exercise frequency
  if (data.exercise_frequency === "two_to_three_times") lifestyleScore += 6;
  else if (data.exercise_frequency === "daily") lifestyleScore += 4;
  else lifestyleScore += 2;

  // Sleep quality
  if (data.sleep_quality >= 7) lifestyleScore += 4;
  else if (data.sleep_quality >= 5) lifestyleScore += 2;
  else lifestyleScore += 1;

  // Emotional support
  if (data.emotional_support_at_home) lifestyleScore += 2;

  // Environmental factors (max 30 points)
  // Income range (higher income generally means better access to care)
  if (data.household_income_range === "above_1l") environmentalScore += 10;
  else if (data.household_income_range === "from_50k_to_1l") environmentalScore += 8;
  else if (data.household_income_range === "from_10k_to_50k") environmentalScore += 6;
  else environmentalScore += 4;

  // Living area (urban areas typically have better medical facilities)
  if (data.living_area === "urban") environmentalScore += 8;
  else if (data.living_area === "semi_urban") environmentalScore += 6;
  else environmentalScore += 4;

  // Work stress level (lower is better)
  if (data.work_stress_level === "low") environmentalScore += 6;
  else if (data.work_stress_level === "medium") environmentalScore += 4;
  else environmentalScore += 2;

  // Pollution exposure (lower is better)
  if (data.pollution_exposure === "low") environmentalScore += 6;
  else if (data.pollution_exposure === "moderate") environmentalScore += 4;
  else environmentalScore += 2;

  const totalScore = biologicalScore + lifestyleScore + environmentalScore;
  const maxScore = 100;
  const percentage = Math.round((totalScore / maxScore) * 100);

  let category = "Low";
  if (percentage >= 80) category = "Excellent";
  else if (percentage >= 70) category = "Good";
  else if (percentage >= 60) category = "Fair";
  else if (percentage >= 50) category = "Moderate";

  return {
    totalScore,
    maxScore,
    percentage,
    category,
    factors: {
      biological: biologicalScore,
      lifestyle: lifestyleScore,
      environmental: environmentalScore,
    },
  };
}
