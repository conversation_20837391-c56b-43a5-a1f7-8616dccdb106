import { NextRequest } from "next/server";
import { authenticate } from "@/utils/api/authenticate";
import { apiResponse } from "@/utils/api/apiResponse";
import { PrismaClient } from "@/generated/prisma";
import {
  completeIvfScoresSchema,
  updateBiologicalFactorsSchema,
  updateLifestylePsychosocialSchema,
  updateEnvironmentalSocioeconomicSchema,
  stepProgressionSchema,
} from "@/validations/ivf-scores";

const prisma = new PrismaClient();

/**
 * GET /api/v1/ivf-scores
 * Retrieve the current user's IVF scores
 */
export async function GET(req: NextRequest) {
  try {
    const { user, error } = await authenticate(req);
    if (error) return error;

    const ivfScores = await prisma.ivf_scores.findUnique({
      where: { user_id: user.id },
    });

    if (!ivfScores) {
      return apiResponse(404, "IVF scores not found for this user");
    }

    return apiResponse(200, undefined, { ivfScores });
  } catch (error) {
    console.error("GET IVF scores error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * POST /api/v1/ivf-scores
 * Create new IVF scores for the user
 */
export async function POST(req: NextRequest) {
  try {
    const { user, error } = await authenticate(req);
    if (error) return error;

    const body = await req.json();
    const result = completeIvfScoresSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    // Check if user already has IVF scores
    const existingScores = await prisma.ivf_scores.findUnique({
      where: { user_id: user.id },
    });

    if (existingScores) {
      return apiResponse(409, "IVF scores already exist for this user. Use PUT to update.");
    }

    const ivfScores = await prisma.ivf_scores.create({
      data: {
        user_id: user.id,
        ...result.data,
      },
    });

    return apiResponse(201, "IVF scores created successfully", { ivfScores });
  } catch (error) {
    console.error("POST IVF scores error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * PUT /api/v1/ivf-scores
 * Update existing IVF scores for the user
 */
export async function PUT(req: NextRequest) {
  try {
    const { user, error } = await authenticate(req);
    if (error) return error;

    const body = await req.json();
    const { step, ...data } = body;

    // Validate based on step or complete data
    let validationResult;
    if (step === 1) {
      validationResult = updateBiologicalFactorsSchema.safeParse(data);
    } else if (step === 2) {
      validationResult = updateLifestylePsychosocialSchema.safeParse(data);
    } else if (step === 3) {
      validationResult = updateEnvironmentalSocioeconomicSchema.safeParse(data);
    } else {
      // Complete update or step progression
      if (data.current_step !== undefined) {
        validationResult = stepProgressionSchema.safeParse(data);
      } else {
        validationResult = completeIvfScoresSchema.partial().safeParse(data);
      }
    }

    if (!validationResult.success) {
      const errorMessage = validationResult.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    // Check if user has existing IVF scores
    const existingScores = await prisma.ivf_scores.findUnique({
      where: { user_id: user.id },
    });

    if (!existingScores) {
      return apiResponse(404, "IVF scores not found for this user. Use POST to create.");
    }

    const updatedScores = await prisma.ivf_scores.update({
      where: { user_id: user.id },
      data: validationResult.data,
    });

    return apiResponse(200, "IVF scores updated successfully", { ivfScores: updatedScores });
  } catch (error) {
    console.error("PUT IVF scores error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * DELETE /api/v1/ivf-scores
 * Delete IVF scores for the user
 */
export async function DELETE(req: NextRequest) {
  try {
    const { user, error } = await authenticate(req);
    if (error) return error;

    const existingScores = await prisma.ivf_scores.findUnique({
      where: { user_id: user.id },
    });

    if (!existingScores) {
      return apiResponse(404, "IVF scores not found for this user");
    }

    await prisma.ivf_scores.delete({
      where: { user_id: user.id },
    });

    return apiResponse(200, "IVF scores deleted successfully");
  } catch (error) {
    console.error("DELETE IVF scores error:", error);
    return apiResponse(500, "Internal server error");
  }
}