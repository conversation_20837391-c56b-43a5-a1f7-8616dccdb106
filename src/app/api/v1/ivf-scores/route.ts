import { NextRequest } from "next/server";
import { apiResponse } from "@/utils/api/apiResponse";
import { getUserOrGuestContext, getIVFData, saveIVFData } from "@/utils/api/userOrGuest";
import {
  completeIvfScoresSchema,
  updateBiologicalFactorsSchema,
  updateLifestylePsychosocialSchema,
  updateEnvironmentalSocioeconomicSchema,
  stepProgressionSchema,
} from "@/validations/ivf-scores";

/**
 * GET /api/v1/ivf-scores
 * Retrieve IVF scores for authenticated users or guests
 */
export async function GET(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;

    const { success, data, error: dataError } = await getIVFData(context!);

    if (!success) {
      return apiResponse(500, dataError || "Failed to retrieve data");
    }

    if (!data) {
      return apiResponse(404, "IVF scores not found");
    }

    return apiResponse(200, undefined, {
      ivfScores: data,
      isGuest: !context!.isAuthenticated
    });
  } catch (error) {
    console.error("GET IVF scores error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * POST /api/v1/ivf-scores
 * Create new IVF scores for authenticated users or guests
 */
export async function POST(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;

    const body = await req.json();
    const result = completeIvfScoresSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    // Check if data already exists
    const { success: getSuccess, data: existingData } = await getIVFData(context!);

    if (getSuccess && existingData) {
      return apiResponse(409, "IVF scores already exist. Use PUT to update.");
    }

    const { success, data, error: saveError, guestSessionToken } = await saveIVFData(
      context!,
      result.data,
      result.data.current_step || 1
    );

    if (!success) {
      return apiResponse(500, saveError || "Failed to create IVF scores");
    }

    const response = apiResponse(201, "IVF scores created successfully", {
      ivfScores: data,
      isGuest: !context!.isAuthenticated
    });

    // Add guest session token to response headers for guest users
    if (guestSessionToken) {
      response.headers.set("X-Guest-Session", guestSessionToken);
    }

    return response;
  } catch (error) {
    console.error("POST IVF scores error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * PUT /api/v1/ivf-scores
 * Update existing IVF scores for authenticated users or guests
 */
export async function PUT(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;

    const body = await req.json();
    const { step, ...data } = body;

    // Validate based on step or complete data
    let validationResult;
    if (step === 1) {
      validationResult = updateBiologicalFactorsSchema.safeParse(data);
    } else if (step === 2) {
      validationResult = updateLifestylePsychosocialSchema.safeParse(data);
    } else if (step === 3) {
      validationResult = updateEnvironmentalSocioeconomicSchema.safeParse(data);
    } else {
      // Complete update or step progression
      if (data.current_step !== undefined) {
        validationResult = stepProgressionSchema.safeParse(data);
      } else {
        validationResult = completeIvfScoresSchema.partial().safeParse(data);
      }
    }

    if (!validationResult.success) {
      const errorMessage = validationResult.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    // Get existing data to merge with updates
    const { success: getSuccess, data: existingData } = await getIVFData(context!);

    if (!getSuccess || !existingData) {
      return apiResponse(404, "IVF scores not found. Use POST to create.");
    }

    // Merge existing data with updates
    const mergedData = { ...existingData, ...validationResult.data };

    const { success, data: updatedData, error: saveError, guestSessionToken } = await saveIVFData(
      context!,
      mergedData,
      existingData.current_step || 1
    );

    if (!success) {
      return apiResponse(500, saveError || "Failed to update IVF scores");
    }

    const response = apiResponse(200, "IVF scores updated successfully", {
      ivfScores: updatedData,
      isGuest: !context!.isAuthenticated
    });

    // Add guest session token to response headers for guest users
    if (guestSessionToken) {
      response.headers.set("X-Guest-Session", guestSessionToken);
    }

    return response;
  } catch (error) {
    console.error("PUT IVF scores error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * DELETE /api/v1/ivf-scores
 * Delete IVF scores for authenticated users or clear guest session
 */
export async function DELETE(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;

    if (context!.isAuthenticated) {
      // For authenticated users, delete from database
      const { PrismaClient } = await import("@/generated/prisma");
      const prisma = new PrismaClient();

      const existingScores = await prisma.ivf_scores.findUnique({
        where: { user_id: context!.user.id },
      });

      if (!existingScores) {
        return apiResponse(404, "IVF scores not found for this user");
      }

      await prisma.ivf_scores.delete({
        where: { user_id: context!.user.id },
      });

      return apiResponse(200, "IVF scores deleted successfully");
    } else {
      // For guests, clear the session (implementation would depend on your session storage)
      // For now, just return success as guest sessions expire automatically
      return apiResponse(200, "Guest session cleared successfully", {
        message: "Guest data will be automatically cleared when session expires"
      });
    }
  } catch (error) {
    console.error("DELETE IVF scores error:", error);
    return apiResponse(500, "Internal server error");
  }
}