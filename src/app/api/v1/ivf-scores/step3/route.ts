import { NextRequest } from "next/server";
import { authenticate } from "@/utils/api/authenticate";
import { apiResponse } from "@/utils/api/apiResponse";
import { PrismaClient } from "@/generated/prisma";
import {
  environmentalSocioeconomicSchema,
  updateEnvironmentalSocioeconomicSchema,
} from "@/validations/ivf-scores";

const prisma = new PrismaClient();

/**
 * GET /api/v1/ivf-scores/step3
 * Retrieve Step 3 (Environmental & Socioeconomic) data for the current user
 */
export async function GET(req: NextRequest) {
  try {
    const { user, error } = await authenticate(req);
    if (error) return error;

    const ivfScores = await prisma.ivf_scores.findUnique({
      where: { user_id: user.id },
      select: {
        id: true,
        household_income_range: true,
        living_area: true,
        work_stress_level: true,
        pollution_exposure: true,
        occupation_type: true,
        current_step: true,
        created_at: true,
        updated_at: true,
      },
    });

    if (!ivfScores) {
      return apiResponse(404, "IVF scores not found for this user");
    }

    return apiResponse(200, undefined, { 
      step3Data: ivfScores,
      stepCompleted: ivfScores.current_step >= 3
    });
  } catch (error) {
    console.error("GET Step 3 error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * POST /api/v1/ivf-scores/step3
 * Update Step 3 (Environmental & Socioeconomic) data
 */
export async function POST(req: NextRequest) {
  try {
    const { user, error } = await authenticate(req);
    if (error) return error;

    const body = await req.json();
    const result = environmentalSocioeconomicSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    // Check if user has existing IVF scores
    const existingScores = await prisma.ivf_scores.findUnique({
      where: { user_id: user.id },
    });

    if (!existingScores) {
      return apiResponse(400, "Steps 1 and 2 must be completed before Step 3. Please complete previous steps first.");
    }

    // Ensure user has completed Steps 1 and 2
    if (existingScores.current_step < 2) {
      return apiResponse(400, "Steps 1 and 2 must be completed before Step 3.");
    }

    // Update existing record with Step 3 data and mark as completed
    const ivfScores = await prisma.ivf_scores.update({
      where: { user_id: user.id },
      data: {
        ...result.data,
        current_step: 3, // Final step completed
      },
    });

    return apiResponse(200, "Step 3 data saved successfully. IVF scoring process completed!", { 
      ivfScores,
      allStepsCompleted: true
    });
  } catch (error) {
    console.error("POST Step 3 error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * PUT /api/v1/ivf-scores/step3
 * Update Step 3 (Environmental & Socioeconomic) data
 */
export async function PUT(req: NextRequest) {
  try {
    const { user, error } = await authenticate(req);
    if (error) return error;

    const body = await req.json();
    const result = updateEnvironmentalSocioeconomicSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    // Check if user has existing IVF scores
    const existingScores = await prisma.ivf_scores.findUnique({
      where: { user_id: user.id },
    });

    if (!existingScores) {
      return apiResponse(404, "IVF scores not found for this user. Please complete previous steps first.");
    }

    const updatedScores = await prisma.ivf_scores.update({
      where: { user_id: user.id },
      data: result.data,
    });

    return apiResponse(200, "Step 3 data updated successfully", { 
      ivfScores: updatedScores 
    });
  } catch (error) {
    console.error("PUT Step 3 error:", error);
    return apiResponse(500, "Internal server error");
  }
}
