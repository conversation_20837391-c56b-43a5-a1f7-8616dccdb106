import { NextRequest } from "next/server";
import { apiResponse } from "@/utils/api/apiResponse";
import { getUserOrGuestContext, getIVFData, saveIVFData } from "@/utils/api/userOrGuest";
import {
  environmentalSocioeconomicSchema,
  updateEnvironmentalSocioeconomicSchema,
} from "@/validations/ivf-scores";

/**
 * GET /api/v1/ivf-scores/step3
 * Retrieve Step 3 (Environmental & Socioeconomic) data for authenticated users or guests
 */
export async function GET(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;

    const { success, data, error: dataError } = await getIVFData(context!);

    if (!success) {
      return apiResponse(500, dataError || "Failed to retrieve data");
    }

    if (!data) {
      return apiResponse(200, undefined, {
        step3Data: null,
        stepCompleted: false,
        isGuest: !context!.isAuthenticated
      });
    }

    // Extract Step 3 specific data
    const step3Data = {
      id: data.id,
      household_income_range: data.household_income_range,
      living_area: data.living_area,
      work_stress_level: data.work_stress_level,
      pollution_exposure: data.pollution_exposure,
      occupation_type: data.occupation_type,
      current_step: data.current_step,
      created_at: data.created_at,
      updated_at: data.updated_at,
    };

    return apiResponse(200, undefined, {
      step3Data,
      stepCompleted: data.current_step >= 3,
      isGuest: !context!.isAuthenticated
    });
  } catch (error) {
    console.error("GET Step 3 error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * POST /api/v1/ivf-scores/step3
 * Update Step 3 (Environmental & Socioeconomic) data for authenticated users or guests
 */
export async function POST(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;

    const body = await req.json();
    const result = environmentalSocioeconomicSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    // Check if user has existing data and completed Steps 1 and 2
    const { success: getSuccess, data: existingData } = await getIVFData(context!);

    if (!getSuccess || !existingData) {
      return apiResponse(400, "Steps 1 and 2 must be completed before Step 3. Please complete previous steps first.");
    }

    // Ensure user has completed Steps 1 and 2
    if (existingData.current_step < 2) {
      return apiResponse(400, "Steps 1 and 2 must be completed before Step 3.");
    }

    // Merge existing data with Step 3 data
    const mergedData = { ...existingData, ...result.data };

    const { success, data, error: saveError, guestSessionToken } = await saveIVFData(
      context!,
      mergedData,
      3 // Final step completed
    );

    if (!success) {
      return apiResponse(500, saveError || "Failed to save Step 3 data");
    }

    const response = apiResponse(200, "Step 3 data saved successfully. IVF scoring process completed!", {
      ivfScores: data,
      allStepsCompleted: true,
      isGuest: !context!.isAuthenticated,
      // For guests, they now need to verify email to see results
      nextAction: !context!.isAuthenticated ? "email_verification" : "view_results"
    });

    // Add guest session token to response headers for guest users
    if (guestSessionToken) {
      response.headers.set("X-Guest-Session", guestSessionToken);
    }

    return response;
  } catch (error) {
    console.error("POST Step 3 error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * PUT /api/v1/ivf-scores/step3
 * Update Step 3 (Environmental & Socioeconomic) data for authenticated users or guests
 */
export async function PUT(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;

    const body = await req.json();
    const result = updateEnvironmentalSocioeconomicSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    // Get existing data to merge with updates
    const { success: getSuccess, data: existingData } = await getIVFData(context!);

    if (!getSuccess || !existingData) {
      return apiResponse(404, "IVF scores not found. Please complete previous steps first.");
    }

    // Merge existing data with updates
    const mergedData = { ...existingData, ...result.data };

    const { success, data, error: saveError, guestSessionToken } = await saveIVFData(
      context!,
      mergedData,
      existingData.current_step || 3
    );

    if (!success) {
      return apiResponse(500, saveError || "Failed to update Step 3 data");
    }

    const response = apiResponse(200, "Step 3 data updated successfully", {
      ivfScores: data,
      isGuest: !context!.isAuthenticated
    });

    // Add guest session token to response headers for guest users
    if (guestSessionToken) {
      response.headers.set("X-Guest-Session", guestSessionToken);
    }

    return response;
  } catch (error) {
    console.error("PUT Step 3 error:", error);
    return apiResponse(500, "Internal server error");
  }
}
