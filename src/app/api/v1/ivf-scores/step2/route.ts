import { NextRequest } from "next/server";
import { authenticate } from "@/utils/api/authenticate";
import { apiResponse } from "@/utils/api/apiResponse";
import { PrismaClient } from "@/generated/prisma";
import {
  lifestylePsychosocialSchema,
  updateLifestylePsychosocialSchema,
} from "@/validations/ivf-scores";

const prisma = new PrismaClient();

/**
 * GET /api/v1/ivf-scores/step2
 * Retrieve Step 2 (Lifestyle & Psychosocial) data for the current user
 */
export async function GET(req: NextRequest) {
  try {
    const { user, error } = await authenticate(req);
    if (error) return error;

    const ivfScores = await prisma.ivf_scores.findUnique({
      where: { user_id: user.id },
      select: {
        id: true,
        stress_level: true,
        diet_type: true,
        exercise_frequency: true,
        sleep_quality: true,
        emotional_support_at_home: true,
        smoking_or_alcohol_habits: true,
        current_step: true,
        created_at: true,
        updated_at: true,
      },
    });

    if (!ivfScores) {
      return apiResponse(404, "IVF scores not found for this user");
    }

    return apiResponse(200, undefined, { 
      step2Data: ivfScores,
      stepCompleted: ivfScores.current_step >= 2
    });
  } catch (error) {
    console.error("GET Step 2 error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * POST /api/v1/ivf-scores/step2
 * Update Step 2 (Lifestyle & Psychosocial) data
 */
export async function POST(req: NextRequest) {
  try {
    const { user, error } = await authenticate(req);
    if (error) return error;

    const body = await req.json();
    const result = lifestylePsychosocialSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    // Check if user has existing IVF scores
    const existingScores = await prisma.ivf_scores.findUnique({
      where: { user_id: user.id },
    });

    if (!existingScores) {
      return apiResponse(400, "Step 1 must be completed before Step 2. Please complete Step 1 first.");
    }

    // Ensure user has completed Step 1
    if (existingScores.current_step < 1) {
      return apiResponse(400, "Step 1 must be completed before Step 2.");
    }

    // Update existing record with Step 2 data
    const ivfScores = await prisma.ivf_scores.update({
      where: { user_id: user.id },
      data: {
        ...result.data,
        current_step: Math.max(existingScores.current_step, 2),
      },
    });

    return apiResponse(200, "Step 2 data saved successfully", { 
      ivfScores,
      nextStep: 3
    });
  } catch (error) {
    console.error("POST Step 2 error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * PUT /api/v1/ivf-scores/step2
 * Update Step 2 (Lifestyle & Psychosocial) data
 */
export async function PUT(req: NextRequest) {
  try {
    const { user, error } = await authenticate(req);
    if (error) return error;

    const body = await req.json();
    const result = updateLifestylePsychosocialSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    // Check if user has existing IVF scores
    const existingScores = await prisma.ivf_scores.findUnique({
      where: { user_id: user.id },
    });

    if (!existingScores) {
      return apiResponse(404, "IVF scores not found for this user. Please complete Step 1 first.");
    }

    const updatedScores = await prisma.ivf_scores.update({
      where: { user_id: user.id },
      data: result.data,
    });

    return apiResponse(200, "Step 2 data updated successfully", { 
      ivfScores: updatedScores 
    });
  } catch (error) {
    console.error("PUT Step 2 error:", error);
    return apiResponse(500, "Internal server error");
  }
}
