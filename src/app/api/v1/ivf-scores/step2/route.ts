import { NextRequest } from "next/server";
import { apiResponse } from "@/utils/api/apiResponse";
import { getUserOrGuestContext, getIVFData, saveIVFData } from "@/utils/api/userOrGuest";
import {
  lifestylePsychosocialSchema,
  updateLifestylePsychosocialSchema,
} from "@/validations/ivf-scores";

/**
 * GET /api/v1/ivf-scores/step2
 * Retrieve Step 2 (Lifestyle & Psychosocial) data for authenticated users or guests
 */
export async function GET(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;

    const { success, data, error: dataError } = await getIVFData(context!);

    if (!success) {
      return apiResponse(500, dataError || "Failed to retrieve data");
    }

    if (!data) {
      return apiResponse(200, undefined, {
        step2Data: null,
        stepCompleted: false,
        isGuest: !context!.isAuthenticated
      });
    }

    // Extract Step 2 specific data
    const step2Data = {
      id: data.id,
      stress_level: data.stress_level,
      diet_type: data.diet_type,
      exercise_frequency: data.exercise_frequency,
      sleep_quality: data.sleep_quality,
      emotional_support_at_home: data.emotional_support_at_home,
      smoking_or_alcohol_habits: data.smoking_or_alcohol_habits,
      current_step: data.current_step,
      created_at: data.created_at,
      updated_at: data.updated_at,
    };

    return apiResponse(200, undefined, {
      step2Data,
      stepCompleted: data.current_step >= 2,
      isGuest: !context!.isAuthenticated
    });
  } catch (error) {
    console.error("GET Step 2 error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * POST /api/v1/ivf-scores/step2
 * Update Step 2 (Lifestyle & Psychosocial) data for authenticated users or guests
 */
export async function POST(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;

    const body = await req.json();
    const result = lifestylePsychosocialSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    // Check if user has existing data and completed Step 1
    const { success: getSuccess, data: existingData } = await getIVFData(context!);

    if (!getSuccess || !existingData) {
      return apiResponse(400, "Step 1 must be completed before Step 2. Please complete Step 1 first.");
    }

    // Ensure user has completed Step 1
    if (existingData.current_step < 1) {
      return apiResponse(400, "Step 1 must be completed before Step 2.");
    }

    // Merge existing data with Step 2 data
    const mergedData = { ...existingData, ...result.data };

    const { success, data, error: saveError, guestSessionToken } = await saveIVFData(
      context!,
      mergedData,
      Math.max(existingData.current_step, 2)
    );

    if (!success) {
      return apiResponse(500, saveError || "Failed to save Step 2 data");
    }

    const response = apiResponse(200, "Step 2 data saved successfully", {
      ivfScores: data,
      nextStep: 3,
      isGuest: !context!.isAuthenticated
    });

    // Add guest session token to response headers for guest users
    if (guestSessionToken) {
      response.headers.set("X-Guest-Session", guestSessionToken);
    }

    return response;
  } catch (error) {
    console.error("POST Step 2 error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * PUT /api/v1/ivf-scores/step2
 * Update Step 2 (Lifestyle & Psychosocial) data for authenticated users or guests
 */
export async function PUT(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;

    const body = await req.json();
    const result = updateLifestylePsychosocialSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    // Get existing data to merge with updates
    const { success: getSuccess, data: existingData } = await getIVFData(context!);

    if (!getSuccess || !existingData) {
      return apiResponse(404, "IVF scores not found. Please complete Step 1 first.");
    }

    // Merge existing data with updates
    const mergedData = { ...existingData, ...result.data };

    const { success, data, error: saveError, guestSessionToken } = await saveIVFData(
      context!,
      mergedData,
      existingData.current_step || 2
    );

    if (!success) {
      return apiResponse(500, saveError || "Failed to update Step 2 data");
    }

    const response = apiResponse(200, "Step 2 data updated successfully", {
      ivfScores: data,
      isGuest: !context!.isAuthenticated
    });

    // Add guest session token to response headers for guest users
    if (guestSessionToken) {
      response.headers.set("X-Guest-Session", guestSessionToken);
    }

    return response;
  } catch (error) {
    console.error("PUT Step 2 error:", error);
    return apiResponse(500, "Internal server error");
  }
}
