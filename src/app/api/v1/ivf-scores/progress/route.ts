import { NextRequest } from "next/server";
import { authenticate } from "@/utils/api/authenticate";
import { apiResponse } from "@/utils/api/apiResponse";
import { PrismaClient } from "@/generated/prisma";
import { stepProgressionSchema } from "@/validations/ivf-scores";

const prisma = new PrismaClient();

/**
 * GET /api/v1/ivf-scores/progress
 * Get the current progress and step information for the user
 */
export async function GET(req: NextRequest) {
  try {
    const { user, error } = await authenticate(req);
    if (error) return error;

    const ivfScores = await prisma.ivf_scores.findUnique({
      where: { user_id: user.id },
      select: {
        id: true,
        current_step: true,
        created_at: true,
        updated_at: true,
      },
    });

    if (!ivfScores) {
      return apiResponse(200, undefined, {
        hasStarted: false,
        currentStep: 0,
        nextStep: 1,
        stepsCompleted: [],
        totalSteps: 3,
        progressPercentage: 0,
        stepDescriptions: {
          1: "Biological Factors",
          2: "Lifestyle & Psychosocial",
          3: "Environmental & Socioeconomic Factors"
        }
      });
    }

    const stepsCompleted = [];
    for (let i = 1; i <= ivfScores.current_step; i++) {
      stepsCompleted.push(i);
    }

    const progressPercentage = Math.round((ivfScores.current_step / 3) * 100);
    const nextStep = ivfScores.current_step < 3 ? ivfScores.current_step + 1 : null;

    return apiResponse(200, undefined, {
      hasStarted: true,
      currentStep: ivfScores.current_step,
      nextStep,
      stepsCompleted,
      totalSteps: 3,
      progressPercentage,
      isCompleted: ivfScores.current_step === 3,
      stepDescriptions: {
        1: "Biological Factors",
        2: "Lifestyle & Psychosocial", 
        3: "Environmental & Socioeconomic Factors"
      },
      lastUpdated: ivfScores.updated_at
    });
  } catch (error) {
    console.error("GET progress error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * PUT /api/v1/ivf-scores/progress
 * Update the current step (for manual step progression)
 */
export async function PUT(req: NextRequest) {
  try {
    const { user, error } = await authenticate(req);
    if (error) return error;

    const body = await req.json();
    const result = stepProgressionSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    // Check if user has existing IVF scores
    const existingScores = await prisma.ivf_scores.findUnique({
      where: { user_id: user.id },
    });

    if (!existingScores) {
      return apiResponse(404, "IVF scores not found for this user. Please start with Step 1.");
    }

    // Prevent going backwards or skipping steps
    if (result.data.current_step < existingScores.current_step) {
      return apiResponse(400, "Cannot go backwards in steps. Current progress will be maintained.");
    }

    if (result.data.current_step > existingScores.current_step + 1) {
      return apiResponse(400, "Cannot skip steps. Please complete steps in order.");
    }

    const updatedScores = await prisma.ivf_scores.update({
      where: { user_id: user.id },
      data: {
        current_step: result.data.current_step,
      },
    });

    return apiResponse(200, "Step progression updated successfully", {
      currentStep: updatedScores.current_step,
      nextStep: updatedScores.current_step < 3 ? updatedScores.current_step + 1 : null,
      isCompleted: updatedScores.current_step === 3
    });
  } catch (error) {
    console.error("PUT progress error:", error);
    return apiResponse(500, "Internal server error");
  }
}
