import { NextRequest } from "next/server";
import { apiResponse } from "@/utils/api/apiResponse";
import { getUserOrGuestContext, getIVFData, saveIVFData } from "@/utils/api/userOrGuest";
import { stepProgressionSchema } from "@/validations/ivf-scores";

/**
 * GET /api/v1/ivf-scores/progress
 * Get the current progress and step information for authenticated users or guests
 */
export async function GET(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;

    const { success, data, error: dataError } = await getIVFData(context!);

    if (!success) {
      return apiResponse(500, dataError || "Failed to retrieve progress data");
    }

    if (!data) {
      return apiResponse(200, undefined, {
        hasStarted: false,
        currentStep: 0,
        nextStep: 1,
        stepsCompleted: [],
        totalSteps: 3,
        progressPercentage: 0,
        isGuest: !context!.isAuthenticated,
        stepDescriptions: {
          1: "Biological Factors",
          2: "Lifestyle & Psychosocial",
          3: "Environmental & Socioeconomic Factors"
        }
      });
    }

    const currentStep = data.current_step || 0;
    const stepsCompleted = [];
    for (let i = 1; i <= currentStep; i++) {
      stepsCompleted.push(i);
    }

    const progressPercentage = Math.round((currentStep / 3) * 100);
    const nextStep = currentStep < 3 ? currentStep + 1 : null;

    return apiResponse(200, undefined, {
      hasStarted: true,
      currentStep,
      nextStep,
      stepsCompleted,
      totalSteps: 3,
      progressPercentage,
      isCompleted: currentStep === 3,
      isGuest: !context!.isAuthenticated,
      stepDescriptions: {
        1: "Biological Factors",
        2: "Lifestyle & Psychosocial",
        3: "Environmental & Socioeconomic Factors"
      },
      lastUpdated: data.updated_at,
      // For guests who completed all steps, show next action
      nextAction: !context!.isAuthenticated && currentStep === 3 ? "email_verification" : null
    });
  } catch (error) {
    console.error("GET progress error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * PUT /api/v1/ivf-scores/progress
 * Update the current step (for manual step progression) for authenticated users or guests
 */
export async function PUT(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;

    const body = await req.json();
    const result = stepProgressionSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    // Get existing data
    const { success: getSuccess, data: existingData } = await getIVFData(context!);

    if (!getSuccess || !existingData) {
      return apiResponse(404, "IVF scores not found. Please start with Step 1.");
    }

    const currentStep = existingData.current_step || 0;

    // Prevent going backwards or skipping steps
    if (result.data.current_step < currentStep) {
      return apiResponse(400, "Cannot go backwards in steps. Current progress will be maintained.");
    }

    if (result.data.current_step > currentStep + 1) {
      return apiResponse(400, "Cannot skip steps. Please complete steps in order.");
    }

    // Update step progression
    const { success, error: saveError, guestSessionToken } = await saveIVFData(
      context!,
      existingData,
      result.data.current_step
    );

    if (!success) {
      return apiResponse(500, saveError || "Failed to update step progression");
    }

    const response = apiResponse(200, "Step progression updated successfully", {
      currentStep: result.data.current_step,
      nextStep: result.data.current_step < 3 ? result.data.current_step + 1 : null,
      isCompleted: result.data.current_step === 3,
      isGuest: !context!.isAuthenticated
    });

    // Add guest session token to response headers for guest users
    if (guestSessionToken) {
      response.headers.set("X-Guest-Session", guestSessionToken);
    }

    return response;
  } catch (error) {
    console.error("PUT progress error:", error);
    return apiResponse(500, "Internal server error");
  }
}
