import { NextRequest } from "next/server";
import { GET, POST, PUT, DELETE } from "./route";
import { authenticate } from "@/utils/api/authenticate";
import { PrismaClient } from "@/generated/prisma";

// Mock the authenticate function
jest.mock("@/utils/api/authenticate", () => ({
  authenticate: jest.fn(),
}));

// Mock the Prisma client
jest.mock("@/generated/prisma", () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    ivf_scores: {
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
  })),
}));


describe("IVF Scores API", () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let mockPrisma: any;
  let mockRequest: NextRequest;

  beforeEach(() => {
    mockPrisma = new PrismaClient();
    mockRequest = {
      json: jest.fn(),
    } as unknown as NextRequest;
    jest.clearAllMocks();
  });

  describe("GET /api/v1/ivf-scores", () => {
    it("should return 401 if user is not authenticated", async () => {
      authenticate.mockResolvedValue({
        error: new Response(JSON.stringify({ error: "Unauthorized" }), {
          status: 401,
        }),
      });

      const response = await GET(mockRequest);
      expect(response.status).toBe(401);
    });

    it("should return 404 if IVF scores not found", async () => {
      authenticate.mockResolvedValue({
        user: { id: "user-123" },
        token: "token-123",
      });
      mockPrisma.ivf_scores.findUnique.mockResolvedValue(null);

      const response = await GET(mockRequest);
      expect(response.status).toBe(404);
    });

    it("should return IVF scores if found", async () => {
      const mockIvfScores = {
        id: "ivf-123",
        user_id: "user-123",
        age: 30,
        current_step: 2,
      };

      authenticate.mockResolvedValue({
        user: { id: "user-123" },
        token: "token-123",
      });
      mockPrisma.ivf_scores.findUnique.mockResolvedValue(mockIvfScores);

      const response = await GET(mockRequest);
      expect(response.status).toBe(200);
      
      const responseData = await response.json();
      expect(responseData.ivfScores).toEqual(mockIvfScores);
    });
  });

  describe("POST /api/v1/ivf-scores", () => {
    it("should return 400 for invalid data", async () => {
      authenticate.mockResolvedValue({
        user: { id: "user-123" },
        token: "token-123",
      });
      mockRequest.json = jest.fn().mockResolvedValue({
        age: "invalid", // Should be number
      });

      const response = await POST(mockRequest);
      expect(response.status).toBe(400);
    });

    it("should return 409 if IVF scores already exist", async () => {
      authenticate.mockResolvedValue({
        user: { id: "user-123" },
        token: "token-123",
      });
      mockRequest.json = jest.fn().mockResolvedValue({
        age: 30,
        height: 165,
        weight: 60,
        menstrual_regularity: "regular",
        infertility_duration: "over_12_months",
        ivf_attempts: 1,
        known_conditions: "",
        stress_level: 5,
        diet_type: "balanced",
        exercise_frequency: "daily",
        sleep_quality: 7,
        emotional_support_at_home: true,
        smoking_or_alcohol_habits: "",
        household_income_range: "from_50k_to_1l",
        living_area: "urban",
        work_stress_level: "medium",
        pollution_exposure: "low",
        occupation_type: "desk_job",
      });
      mockPrisma.ivf_scores.findUnique.mockResolvedValue({ id: "existing" });

      const response = await POST(mockRequest);
      expect(response.status).toBe(409);
    });
  });

  describe("PUT /api/v1/ivf-scores", () => {
    it("should return 404 if IVF scores not found", async () => {
      authenticate.mockResolvedValue({
        user: { id: "user-123" },
        token: "token-123",
      });
      mockRequest.json = jest.fn().mockResolvedValue({
        age: 31,
      });
      mockPrisma.ivf_scores.findUnique.mockResolvedValue(null);

      const response = await PUT(mockRequest);
      expect(response.status).toBe(404);
    });
  });

  describe("DELETE /api/v1/ivf-scores", () => {
    it("should return 404 if IVF scores not found", async () => {
      authenticate.mockResolvedValue({
        user: { id: "user-123" },
        token: "token-123",
      });
      mockPrisma.ivf_scores.findUnique.mockResolvedValue(null);

      const response = await DELETE(mockRequest);
      expect(response.status).toBe(404);
    });

    it("should successfully delete IVF scores", async () => {
      authenticate.mockResolvedValue({
        user: { id: "user-123" },
        token: "token-123",
      });
      mockPrisma.ivf_scores.findUnique.mockResolvedValue({ id: "ivf-123" });
      mockPrisma.ivf_scores.delete.mockResolvedValue({ id: "ivf-123" });

      const response = await DELETE(mockRequest);
      expect(response.status).toBe(200);
    });
  });
});
