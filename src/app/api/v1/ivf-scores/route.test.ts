import { NextRequest } from "next/server";
import { GET, POST } from "./route";

// Mock the getUserOrGuestContext function
jest.mock("@/utils/api/userOrGuest", () => ({
  getUserOrGuestContext: jest.fn(),
  getIVFData: jest.fn(),
  saveIVFData: jest.fn(),
}));

// eslint-disable-next-line @typescript-eslint/no-require-imports
const { getUserOrGuestContext, getIVFData, saveIVFData } = require("@/utils/api/userOrGuest");

describe("IVF Scores API", () => {
  let mockRequest: NextRequest;

  beforeEach(() => {
    mockRequest = {
      json: jest.fn(),
    } as unknown as NextRequest;
    jest.clearAllMocks();
  });

  describe("GET /api/v1/ivf-scores", () => {
    it("should return error if context retrieval fails", async () => {
      getUserOrGuestContext.mockResolvedValue({
        context: null,
        error: new Response(JSON.stringify({ error: "Context error" }), {
          status: 500,
        }),
      });

      const response = await GET(mockRequest);
      expect(response.status).toBe(500);
    });

    it("should return 404 if IVF scores not found", async () => {
      getUserOrGuestContext.mockResolvedValue({
        context: { isAuthenticated: true, user: { id: "user-123" } },
        error: null,
      });
      getIVFData.mockResolvedValue({
        success: true,
        data: null,
      });

      const response = await GET(mockRequest);
      expect(response.status).toBe(404);
    });

    it("should return IVF scores for authenticated user", async () => {
      const mockIvfScores = {
        id: "ivf-123",
        age: 30,
        current_step: 2,
      };

      getUserOrGuestContext.mockResolvedValue({
        context: { isAuthenticated: true, user: { id: "user-123" } },
        error: null,
      });
      getIVFData.mockResolvedValue({
        success: true,
        data: mockIvfScores,
      });

      const response = await GET(mockRequest);
      expect(response.status).toBe(200);

      const responseData = await response.json();
      expect(responseData.ivfScores).toEqual(mockIvfScores);
      expect(responseData.isGuest).toBe(false);
    });

    it("should return IVF scores for guest user", async () => {
      const mockIvfScores = {
        id: "guest-123",
        age: 30,
        current_step: 2,
      };

      getUserOrGuestContext.mockResolvedValue({
        context: { isAuthenticated: false, guestSessionToken: "guest-token" },
        error: null,
      });
      getIVFData.mockResolvedValue({
        success: true,
        data: mockIvfScores,
      });

      const response = await GET(mockRequest);
      expect(response.status).toBe(200);

      const responseData = await response.json();
      expect(responseData.ivfScores).toEqual(mockIvfScores);
      expect(responseData.isGuest).toBe(true);
    });
  });

  describe("POST /api/v1/ivf-scores", () => {
    it("should return 400 for invalid data", async () => {
      getUserOrGuestContext.mockResolvedValue({
        context: { isAuthenticated: false, guestSessionToken: "guest-token" },
        error: null,
      });
      mockRequest.json = jest.fn().mockResolvedValue({
        age: "invalid", // Should be number
      });

      const response = await POST(mockRequest);
      expect(response.status).toBe(400);
    });

    it("should return 409 if IVF scores already exist", async () => {
      getUserOrGuestContext.mockResolvedValue({
        context: { isAuthenticated: false, guestSessionToken: "guest-token" },
        error: null,
      });
      mockRequest.json = jest.fn().mockResolvedValue({
        age: 30,
        height: 165,
        weight: 60,
        menstrual_regularity: "regular",
        infertility_duration: "over_12_months",
        ivf_attempts: 1,
        known_conditions: "",
        stress_level: 5,
        diet_type: "balanced",
        exercise_frequency: "daily",
        sleep_quality: 7,
        emotional_support_at_home: true,
        smoking_or_alcohol_habits: "",
        household_income_range: "from_50k_to_1l",
        living_area: "urban",
        work_stress_level: "medium",
        pollution_exposure: "low",
        occupation_type: "desk_job",
      });
      getIVFData.mockResolvedValue({
        success: true,
        data: { id: "existing" },
      });

      const response = await POST(mockRequest);
      expect(response.status).toBe(409);
    });

    it("should create IVF scores for guest user", async () => {
      const mockCreatedData = {
        id: "new-ivf-123",
        age: 30,
        current_step: 1,
      };

      getUserOrGuestContext.mockResolvedValue({
        context: { isAuthenticated: false, guestSessionToken: "guest-token" },
        error: null,
      });
      mockRequest.json = jest.fn().mockResolvedValue({
        age: 30,
        height: 165,
        weight: 60,
        menstrual_regularity: "regular",
        infertility_duration: "over_12_months",
        ivf_attempts: 1,
        known_conditions: "",
        stress_level: 5,
        diet_type: "balanced",
        exercise_frequency: "daily",
        sleep_quality: 7,
        emotional_support_at_home: true,
        smoking_or_alcohol_habits: "",
        household_income_range: "from_50k_to_1l",
        living_area: "urban",
        work_stress_level: "medium",
        pollution_exposure: "low",
        occupation_type: "desk_job",
      });
      getIVFData.mockResolvedValue({
        success: true,
        data: null,
      });
      saveIVFData.mockResolvedValue({
        success: true,
        data: mockCreatedData,
        guestSessionToken: "guest-token",
      });

      const response = await POST(mockRequest);
      expect(response.status).toBe(201);

      const responseData = await response.json();
      expect(responseData.ivfScores).toEqual(mockCreatedData);
      expect(responseData.isGuest).toBe(true);
    });
  });
});
