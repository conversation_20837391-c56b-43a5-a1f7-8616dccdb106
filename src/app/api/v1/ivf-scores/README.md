# IVF Scores API Documentation

This API provides endpoints for managing IVF (In Vitro Fertilization) scores through a three-step process. All endpoints require authentication using the Bear<PERSON> token in the Authorization header.

## Authentication

All endpoints require authentication. Include the Bearer token in the Authorization header:

```
Authorization: Bearer <your-access-token>
```

## API Endpoints

### Main IVF Scores Endpoints

#### `GET /api/v1/ivf-scores`
Retrieve the current user's complete IVF scores.

**Response:**
```json
{
  "ivfScores": {
    "id": "uuid",
    "user_id": "uuid",
    "age": 30,
    "height": 165.5,
    "weight": 60.0,
    // ... all other fields
    "current_step": 3,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

#### `POST /api/v1/ivf-scores`
Create new IVF scores with complete data.

**Request Body:** Complete IVF scores object with all required fields.

#### `PUT /api/v1/ivf-scores`
Update existing IVF scores. Supports partial updates and step-specific updates.

**Request Body:** 
- For step-specific updates: `{ "step": 1, ...stepData }`
- For complete updates: Complete or partial IVF scores object

#### `DELETE /api/v1/ivf-scores`
Delete the user's IVF scores.

### Step-Specific Endpoints

#### Step 1: Biological Factors
**Endpoint:** `/api/v1/ivf-scores/step1`

**Fields:**
- `age` (number, 18-50): User's age
- `height` (number, 100-250): Height in cm
- `weight` (number, 30-200): Weight in kg
- `menstrual_regularity` (enum): "regular" | "irregular"
- `infertility_duration` (enum): "under_6_months" | "six_to_twelve_months" | "over_12_months"
- `ivf_attempts` (number, 0-10): Number of previous IVF attempts
- `known_conditions` (string, optional): Known medical conditions

**Methods:** GET, POST, PUT

#### Step 2: Lifestyle & Psychosocial
**Endpoint:** `/api/v1/ivf-scores/step2`

**Fields:**
- `stress_level` (number, 0-10): Stress level rating
- `diet_type` (enum): "balanced" | "vegetarian" | "junk_heavy" | "skipping_meals"
- `exercise_frequency` (enum): "daily" | "two_to_three_times" | "rarely_or_never"
- `sleep_quality` (number, 0-10): Sleep quality rating
- `emotional_support_at_home` (boolean): Has emotional support at home
- `smoking_or_alcohol_habits` (string, optional): Smoking or alcohol habits

**Methods:** GET, POST, PUT

#### Step 3: Environmental & Socioeconomic
**Endpoint:** `/api/v1/ivf-scores/step3`

**Fields:**
- `household_income_range` (enum): "under_10k" | "from_10k_to_50k" | "from_50k_to_1l" | "above_1l"
- `living_area` (enum): "urban" | "semi_urban" | "rural"
- `work_stress_level` (enum): "low" | "medium" | "high"
- `pollution_exposure` (enum): "high" | "moderate" | "low"
- `occupation_type` (enum): "desk_job" | "field_work" | "night_shift" | "homemaker"

**Methods:** GET, POST, PUT

### Progress Tracking

#### `GET /api/v1/ivf-scores/progress`
Get the current progress and step information.

**Response:**
```json
{
  "hasStarted": true,
  "currentStep": 2,
  "nextStep": 3,
  "stepsCompleted": [1, 2],
  "totalSteps": 3,
  "progressPercentage": 67,
  "isCompleted": false,
  "stepDescriptions": {
    "1": "Biological Factors",
    "2": "Lifestyle & Psychosocial",
    "3": "Environmental & Socioeconomic Factors"
  },
  "lastUpdated": "2024-01-01T00:00:00Z"
}
```

#### `PUT /api/v1/ivf-scores/progress`
Update the current step manually.

**Request Body:**
```json
{
  "current_step": 2
}
```

## Validation

All endpoints use Zod validation schemas located in `/src/validations/ivf-scores.ts`. These schemas can be reused for client-side validation with React Hook Form.

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": "Error message describing what went wrong"
}
```

Common HTTP status codes:
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (authentication required)
- `404`: Not Found (resource doesn't exist)
- `409`: Conflict (resource already exists)
- `500`: Internal Server Error

## Step Flow

1. **Step 1**: Users must complete biological factors first
2. **Step 2**: Can only be accessed after Step 1 is completed
3. **Step 3**: Can only be accessed after Steps 1 and 2 are completed

The `current_step` field tracks the user's progress and prevents skipping steps.

## Usage Examples

### Complete Step 1
```javascript
const response = await fetch('/api/v1/ivf-scores/step1', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer <token>',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    age: 30,
    height: 165.5,
    weight: 60.0,
    menstrual_regularity: 'regular',
    infertility_duration: 'over_12_months',
    ivf_attempts: 1,
    known_conditions: 'PCOS'
  })
});
```

### Check Progress
```javascript
const response = await fetch('/api/v1/ivf-scores/progress', {
  headers: {
    'Authorization': 'Bearer <token>'
  }
});
```
