# IVF Scores API Documentation

This API provides endpoints for managing IVF (In Vitro Fertilization) scores through a three-step process. The API supports both **authenticated users** and **guest users** with different access levels.

## Authentication & Guest Access

### Guest Users (Steps 1-3)
- **No authentication required** for Steps 1-3
- Guest sessions are automatically created and managed
- Guest session token is returned in `X-Guest-Session` response header
- Include guest session token in subsequent requests via `X-Guest-Session` header

### Authenticated Users
- Include the Bearer token in the Authorization header:
```
Authorization: Bearer <your-access-token>
```

### Results Access
- **Authenticated users**: Can view results immediately after completing Step 3
- **Guest users**: Must verify email after Step 3 to view results

## API Endpoints

### Main IVF Scores Endpoints

#### `GET /api/v1/ivf-scores`
Retrieve IVF scores for authenticated users or guests.

**Headers (for guests):**
```
X-Guest-Session: <guest-session-token>
```

**Response:**
```json
{
  "ivfScores": {
    "id": "uuid",
    "age": 30,
    "height": 165.5,
    "weight": 60.0,
    // ... all other fields
    "current_step": 3,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "isGuest": true
}
```

#### `POST /api/v1/ivf-scores`
Create new IVF scores for authenticated users or guests.

**Request Body:** Complete IVF scores object with all required fields.
**Response Headers (for guests):** `X-Guest-Session: <token>`

#### `PUT /api/v1/ivf-scores`
Update existing IVF scores. Supports partial updates and step-specific updates.

**Request Body:**
- For step-specific updates: `{ "step": 1, ...stepData }`
- For complete updates: Complete or partial IVF scores object

#### `DELETE /api/v1/ivf-scores`
Delete IVF scores (authenticated) or clear guest session.

### Step-Specific Endpoints (No Authentication Required)

#### Step 1: Biological Factors
**Endpoint:** `/api/v1/ivf-scores/step1`
**Authentication:** None required (works for both guests and authenticated users)

**Fields:**
- `age` (number, 18-50): User's age
- `height` (number, 100-250): Height in cm
- `weight` (number, 30-200): Weight in kg
- `menstrual_regularity` (enum): "regular" | "irregular"
- `infertility_duration` (enum): "under_6_months" | "six_to_twelve_months" | "over_12_months"
- `ivf_attempts` (number, 0-10): Number of previous IVF attempts
- `known_conditions` (string, optional): Known medical conditions

**Methods:** GET, POST, PUT
**Response Headers (for guests):** `X-Guest-Session: <token>`

#### Step 2: Lifestyle & Psychosocial
**Endpoint:** `/api/v1/ivf-scores/step2`
**Authentication:** None required (requires Step 1 completion)

**Fields:**
- `stress_level` (number, 0-10): Stress level rating
- `diet_type` (enum): "balanced" | "vegetarian" | "junk_heavy" | "skipping_meals"
- `exercise_frequency` (enum): "daily" | "two_to_three_times" | "rarely_or_never"
- `sleep_quality` (number, 0-10): Sleep quality rating
- `emotional_support_at_home` (boolean): Has emotional support at home
- `smoking_or_alcohol_habits` (string, optional): Smoking or alcohol habits

**Methods:** GET, POST, PUT
**Response Headers (for guests):** `X-Guest-Session: <token>`

#### Step 3: Environmental & Socioeconomic
**Endpoint:** `/api/v1/ivf-scores/step3`
**Authentication:** None required (requires Steps 1-2 completion)

**Fields:**
- `household_income_range` (enum): "under_10k" | "from_10k_to_50k" | "from_50k_to_1l" | "above_1l"
- `living_area` (enum): "urban" | "semi_urban" | "rural"
- `work_stress_level` (enum): "low" | "medium" | "high"
- `pollution_exposure` (enum): "high" | "moderate" | "low"
- `occupation_type` (enum): "desk_job" | "field_work" | "night_shift" | "homemaker"

**Methods:** GET, POST, PUT
**Response Headers (for guests):** `X-Guest-Session: <token>`

**Special Response for Step 3 Completion:**
```json
{
  "ivfScores": { ... },
  "allStepsCompleted": true,
  "isGuest": true,
  "nextAction": "email_verification"
}
```

### Email Verification (Guest Users Only)

#### `POST /api/v1/ivf-scores/verify-email`
Send verification email to guest user after completing Step 3.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "guestSessionToken": "guest_uuid" // optional, can use header
}
```

**Response:**
```json
{
  "message": "Verification email sent successfully",
  "developmentCode": "123456", // Remove in production
  "expiresIn": "10 minutes"
}
```

#### `PUT /api/v1/ivf-scores/verify-email`
Verify email code and mark guest session as verified.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "code": "123456",
  "guestSessionToken": "guest_uuid" // optional, can use header
}
```

### Results Endpoint (Authentication Required)

#### `GET /api/v1/ivf-scores/results`
Get IVF score results and calculations.

**Authentication:**
- **Authenticated users**: Bearer token required
- **Verified guests**: Query parameters `?guestSessionToken=<token>&email=<email>`

**Response:**
```json
{
  "ivfScores": { ... },
  "calculatedScore": {
    "totalScore": 85,
    "maxScore": 100,
    "percentage": 85,
    "category": "Excellent",
    "factors": {
      "biological": 35,
      "lifestyle": 25,
      "environmental": 25
    }
  },
  "isAuthenticated": true,
  "completedAt": "2024-01-01T00:00:00Z"
}
```

#### `POST /api/v1/ivf-scores/results`
Convert verified guest session to authenticated user account.

**Authentication:** Bearer token required
**Request Body:**
```json
{
  "guestSessionToken": "guest_uuid",
  "email": "<EMAIL>"
}
```

### Progress Tracking

#### `GET /api/v1/ivf-scores/progress`
Get the current progress and step information.

**Response:**
```json
{
  "hasStarted": true,
  "currentStep": 2,
  "nextStep": 3,
  "stepsCompleted": [1, 2],
  "totalSteps": 3,
  "progressPercentage": 67,
  "isCompleted": false,
  "isGuest": true,
  "stepDescriptions": {
    "1": "Biological Factors",
    "2": "Lifestyle & Psychosocial",
    "3": "Environmental & Socioeconomic Factors"
  },
  "lastUpdated": "2024-01-01T00:00:00Z",
  "nextAction": "email_verification" // For guests who completed Step 3
}
```

#### `PUT /api/v1/ivf-scores/progress`
Update the current step manually.

**Request Body:**
```json
{
  "current_step": 2
}
```

## Validation

All endpoints use Zod validation schemas located in `/src/validations/ivf-scores.ts`. These schemas can be reused for client-side validation with React Hook Form.

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": "Error message describing what went wrong"
}
```

Common HTTP status codes:
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (authentication required)
- `404`: Not Found (resource doesn't exist)
- `409`: Conflict (resource already exists)
- `500`: Internal Server Error

## Guest vs Authenticated User Flow

### Guest User Flow
1. **Steps 1-3**: Complete without authentication
2. **Email Verification**: Required after Step 3 to view results
3. **View Results**: Access with verified guest session
4. **Optional**: Convert to authenticated account

### Authenticated User Flow
1. **Steps 1-3**: Complete with authentication
2. **View Results**: Immediate access after Step 3

## Step Progression Rules

1. **Step 1**: Can be started by anyone (guest or authenticated)
2. **Step 2**: Requires Step 1 completion
3. **Step 3**: Requires Steps 1 and 2 completion
4. **Results**: Requires authentication OR verified guest email

The `current_step` field tracks progress and prevents skipping steps.

## Usage Examples

### Guest User - Complete Step 1
```javascript
const response = await fetch('/api/v1/ivf-scores/step1', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
    // No authentication required
  },
  body: JSON.stringify({
    age: 30,
    height: 165.5,
    weight: 60.0,
    menstrual_regularity: 'regular',
    infertility_duration: 'over_12_months',
    ivf_attempts: 1,
    known_conditions: 'PCOS'
  })
});

// Save the guest session token from response headers
const guestToken = response.headers.get('X-Guest-Session');
```

### Guest User - Continue with Step 2
```javascript
const response = await fetch('/api/v1/ivf-scores/step2', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-Guest-Session': guestToken // Use saved token
  },
  body: JSON.stringify({
    stress_level: 5,
    diet_type: 'balanced',
    // ... other fields
  })
});
```

### Guest User - Email Verification
```javascript
// Step 1: Request verification email
const emailResponse = await fetch('/api/v1/ivf-scores/verify-email', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-Guest-Session': guestToken
  },
  body: JSON.stringify({
    email: '<EMAIL>'
  })
});

// Step 2: Verify with code
const verifyResponse = await fetch('/api/v1/ivf-scores/verify-email', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'X-Guest-Session': guestToken
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    code: '123456'
  })
});
```

### Guest User - View Results
```javascript
const response = await fetch(
  `/api/v1/ivf-scores/results?guestSessionToken=${guestToken}&email=<EMAIL>`
);
```

### Authenticated User - Complete Step 1
```javascript
const response = await fetch('/api/v1/ivf-scores/step1', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer <token>',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    age: 30,
    height: 165.5,
    weight: 60.0,
    menstrual_regularity: 'regular',
    infertility_duration: 'over_12_months',
    ivf_attempts: 1,
    known_conditions: 'PCOS'
  })
});
```

### Check Progress
```javascript
// Works for both guests and authenticated users
const response = await fetch('/api/v1/ivf-scores/progress', {
  headers: {
    // For authenticated users:
    'Authorization': 'Bearer <token>',
    // For guests:
    'X-Guest-Session': guestToken
  }
});
```
