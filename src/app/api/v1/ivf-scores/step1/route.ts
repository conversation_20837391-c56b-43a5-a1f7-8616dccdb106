import { NextRequest } from "next/server";
import { authenticate } from "@/utils/api/authenticate";
import { apiResponse } from "@/utils/api/apiResponse";
import { PrismaClient } from "@/generated/prisma";
import {
  biologicalFactorsSchema,
  updateBiologicalFactorsSchema,
} from "@/validations/ivf-scores";

const prisma = new PrismaClient();

/**
 * GET /api/v1/ivf-scores/step1
 * Retrieve Step 1 (Biological Factors) data for the current user
 */
export async function GET(req: NextRequest) {
  try {
    const { user, error } = await authenticate(req);
    if (error) return error;

    const ivfScores = await prisma.ivf_scores.findUnique({
      where: { user_id: user.id },
      select: {
        id: true,
        age: true,
        height: true,
        weight: true,
        menstrual_regularity: true,
        infertility_duration: true,
        ivf_attempts: true,
        known_conditions: true,
        current_step: true,
        created_at: true,
        updated_at: true,
      },
    });

    if (!ivfScores) {
      return apiResponse(404, "IVF scores not found for this user");
    }

    return apiResponse(200, undefined, { 
      step1Data: ivfScores,
      stepCompleted: ivfScores.current_step >= 1
    });
  } catch (error) {
    console.error("GET Step 1 error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * POST /api/v1/ivf-scores/step1
 * Create or update Step 1 (Biological Factors) data
 */
export async function POST(req: NextRequest) {
  try {
    const { user, error } = await authenticate(req);
    if (error) return error;

    const body = await req.json();
    const result = biologicalFactorsSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    // Check if user already has IVF scores
    const existingScores = await prisma.ivf_scores.findUnique({
      where: { user_id: user.id },
    });

    let ivfScores;
    if (existingScores) {
      // Update existing record with Step 1 data
      ivfScores = await prisma.ivf_scores.update({
        where: { user_id: user.id },
        data: {
          ...result.data,
          current_step: Math.max(existingScores.current_step, 1),
        },
      });
    } else {
      // Create new record with Step 1 data
      ivfScores = await prisma.ivf_scores.create({
        data: {
          user_id: user.id,
          ...result.data,
          current_step: 1,
          // Set default values for other steps (will be updated later)
          stress_level: 0,
          diet_type: "balanced",
          exercise_frequency: "rarely_or_never",
          sleep_quality: 0,
          emotional_support_at_home: false,
          smoking_or_alcohol_habits: "",
          household_income_range: "under_10k",
          living_area: "urban",
          work_stress_level: "low",
          pollution_exposure: "low",
          occupation_type: "desk_job",
        },
      });
    }

    return apiResponse(200, "Step 1 data saved successfully", { 
      ivfScores,
      nextStep: 2
    });
  } catch (error) {
    console.error("POST Step 1 error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * PUT /api/v1/ivf-scores/step1
 * Update Step 1 (Biological Factors) data
 */
export async function PUT(req: NextRequest) {
  try {
    const { user, error } = await authenticate(req);
    if (error) return error;

    const body = await req.json();
    const result = updateBiologicalFactorsSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    // Check if user has existing IVF scores
    const existingScores = await prisma.ivf_scores.findUnique({
      where: { user_id: user.id },
    });

    if (!existingScores) {
      return apiResponse(404, "IVF scores not found for this user. Use POST to create.");
    }

    const updatedScores = await prisma.ivf_scores.update({
      where: { user_id: user.id },
      data: result.data,
    });

    return apiResponse(200, "Step 1 data updated successfully", { 
      ivfScores: updatedScores 
    });
  } catch (error) {
    console.error("PUT Step 1 error:", error);
    return apiResponse(500, "Internal server error");
  }
}
