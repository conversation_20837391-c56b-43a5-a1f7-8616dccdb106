import { NextRequest } from "next/server";
import { apiResponse } from "@/utils/api/apiResponse";
import { getUserOrGuestContext, getIVFData, saveIVFData } from "@/utils/api/userOrGuest";
import {
  biologicalFactorsSchema,
  updateBiologicalFactorsSchema,
} from "@/validations/ivf-scores";

/**
 * GET /api/v1/ivf-scores/step1
 * Retrieve Step 1 (Biological Factors) data for authenticated users or guests
 */
export async function GET(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;

    const { success, data, error: dataError } = await getIVFData(context!);

    if (!success) {
      return apiResponse(500, dataError || "Failed to retrieve data");
    }

    if (!data) {
      return apiResponse(200, undefined, {
        step1Data: null,
        stepCompleted: false,
        isGuest: !context!.isAuthenticated
      });
    }

    // Extract Step 1 specific data
    const step1Data = {
      id: data.id,
      age: data.age,
      height: data.height,
      weight: data.weight,
      menstrual_regularity: data.menstrual_regularity,
      infertility_duration: data.infertility_duration,
      ivf_attempts: data.ivf_attempts,
      known_conditions: data.known_conditions,
      current_step: data.current_step,
      created_at: data.created_at,
      updated_at: data.updated_at,
    };

    return apiResponse(200, undefined, {
      step1Data,
      stepCompleted: data.current_step >= 1,
      isGuest: !context!.isAuthenticated
    });
  } catch (error) {
    console.error("GET Step 1 error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * POST /api/v1/ivf-scores/step1
 * Create or update Step 1 (Biological Factors) data for authenticated users or guests
 */
export async function POST(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;

    const body = await req.json();
    const result = biologicalFactorsSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    const { success, data, error: saveError, guestSessionToken } = await saveIVFData(
      context!,
      result.data,
      1
    );

    if (!success) {
      return apiResponse(500, saveError || "Failed to save Step 1 data");
    }

    const response = apiResponse(200, "Step 1 data saved successfully", {
      ivfScores: data,
      nextStep: 2,
      isGuest: !context!.isAuthenticated
    });

    // Add guest session token to response headers for guest users
    if (guestSessionToken) {
      response.headers.set("X-Guest-Session", guestSessionToken);
    }

    return response;
  } catch (error) {
    console.error("POST Step 1 error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * PUT /api/v1/ivf-scores/step1
 * Update Step 1 (Biological Factors) data for authenticated users or guests
 */
export async function PUT(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;

    const body = await req.json();
    const result = updateBiologicalFactorsSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    // Get existing data to merge with updates
    const { success: getSuccess, data: existingData } = await getIVFData(context!);

    if (!getSuccess || !existingData) {
      return apiResponse(404, "IVF scores not found. Use POST to create.");
    }

    // Merge existing data with updates
    const mergedData = { ...existingData, ...result.data };

    const { success, data, error: saveError, guestSessionToken } = await saveIVFData(
      context!,
      mergedData,
      existingData.current_step || 1
    );

    if (!success) {
      return apiResponse(500, saveError || "Failed to update Step 1 data");
    }

    const response = apiResponse(200, "Step 1 data updated successfully", {
      ivfScores: data,
      isGuest: !context!.isAuthenticated
    });

    // Add guest session token to response headers for guest users
    if (guestSessionToken) {
      response.headers.set("X-Guest-Session", guestSessionToken);
    }

    return response;
  } catch (error) {
    console.error("PUT Step 1 error:", error);
    return apiResponse(500, "Internal server error");
  }
}
