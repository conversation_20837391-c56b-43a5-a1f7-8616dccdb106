// app/api/graphql/route.ts
import { parseToken, verifyAccessToken } from "@/utils/api/verifyToken";
import { NextRequest } from "next/server";

const SUPABASE_GRAPHQL_URL =
  process.env.NEXT_PUBLIC_SUPABASE_URL! + "/graphql/v1";
const SUPABASE_API_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

function withCorsHeaders(res: Response) {
  res.headers.set("Access-Control-Allow-Origin", "*");
  res.headers.set("Access-Control-Allow-Methods", "GET,POST,OPTIONS");
  res.headers.set(
    "Access-Control-Allow-Headers",
    "Content-Type,Authorization,apikey",
  );
  return res;
}

export async function OPTIONS() {
  return withCorsHeaders(new Response(null, { status: 204 }));
}

// GET request to Supabase GraphQL
export async function GET(req: NextRequest) {
  const url = new URL(req.url);
  const search = url.searchParams.toString();
  const proxyUrl = `${SUPABASE_GRAPHQL_URL}?${search}`;

  const response = await fetch(proxyUrl, {
    method: "GET",
    headers: {
      apikey: SUPABASE_API_KEY,
      authorization: `Bearer ${SUPABASE_API_KEY}`,
    },
  });

  const data = await response.text();

  return withCorsHeaders(
    new Response(data, {
      status: response.status,
      headers: { "Content-Type": "application/json" },
    }),
  );
}

// POST request to Supabase GraphQL
export async function POST(req: NextRequest) {
  const token = parseToken(req);
  if (!token) {
    return new Response(
      JSON.stringify({ error: "Unauthorized: No token provided" }),
      { status: 401 },
    );
  }

  const user = await verifyAccessToken(token);
  if (!user) {
    return new Response(
      JSON.stringify({ error: "Unauthorized: Invalid token" }),
      { status: 401 },
    );
  }

  // 3. Forward the request to Supabase GraphQL with the user's token
  const body = await req.text();
  const response = await fetch(SUPABASE_GRAPHQL_URL, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      apikey: SUPABASE_API_KEY,
      authorization: `Bearer ${token}`,
    },
    body,
  });

  const data = await response.text();
  return withCorsHeaders(
    new Response(data, {
      status: response.status,
      headers: { "Content-Type": "application/json" },
    }),
  );
}
