// app/api/refresh-token/route.ts
import { NextRequest } from "next/server";
import { z } from "zod";

const refreshTokenSchema = z.object({
  refresh_token: z.string().min(1, "Refresh token is required"),
});

function withCorsHeaders(res: Response) {
  res.headers.set("Access-Control-Allow-Origin", "*");
  res.headers.set("Access-Control-Allow-Methods", "POST,OPTIONS");
  res.headers.set(
    "Access-Control-Allow-Headers",
    "Content-Type,Authorization,apikey",
  );
  return res;
}

export async function OPTIONS() {
  return withCorsHeaders(new Response(null, { status: 204 }));
}

export async function POST(req: NextRequest) {
  try {
    const json = await req.json();
    const result = refreshTokenSchema.safeParse(json);

    if (!result.success) {
      return withCorsHeaders(
        new Response(
          JSON.stringify({
            error:
              result.error.issues[0]?.message || "Invalid refresh token format",
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          },
        ),
      );
    }

    const { refresh_token } = result.data;

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SUPABASE_URL}/auth/v1/token?grant_type=refresh_token`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          apikey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        },
        body: JSON.stringify({ refresh_token }),
      },
    );

    const data = await response.json();

    if (!response.ok) {
      console.error("Supabase Refresh Error:", data);
      return withCorsHeaders(
        new Response(
          JSON.stringify({
            error: data.error_description || "Failed to refresh token",
          }),
          {
            status: 401,
            headers: { "Content-Type": "application/json" },
          },
        ),
      );
    }

    return withCorsHeaders(
      new Response(
        JSON.stringify({
          access_token: data.access_token,
          refresh_token: data.refresh_token,
          expires_at: data.expires_at,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        },
      ),
    );
  } catch (err) {
    console.error("Refresh token error:", err);
    return withCorsHeaders(
      new Response(JSON.stringify({ error: "Internal server error" }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }),
    );
  }
}
