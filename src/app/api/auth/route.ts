// pages/api/auth.ts
import { NextRequest } from "next/server";
import { z } from "zod";
import { getServiceRoleSupabase } from "@/utils/supabase/service-role";

const loginSchema = z.object({
  email: z.string().email("Invalid email format").min(1, "Email is required"),
  password: z
    .string()
    .min(6, "Password must be at least 6 characters")
    .max(100, "Password is too long"),
});

function withCorsHeaders(res: Response) {
  res.headers.set("Access-Control-Allow-Origin", "*");
  res.headers.set("Access-Control-Allow-Methods", "POST,OPTIONS");
  res.headers.set(
    "Access-Control-Allow-Headers",
    "Content-Type,Authorization,apikey",
  );
  return res;
}

export async function OPTIONS() {
  return withCorsHeaders(new Response(null, { status: 204 }));
}

export async function POST(req: NextRequest) {
  try {
    const json = await req.json();
    const result = loginSchema.safeParse(json);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => issue.message)
        .join(", ");
      return withCorsHeaders(
        new Response(JSON.stringify({ error: errorMessage }), {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }),
      );
    }

    const { email, password } = result.data;
    const supabase = getServiceRoleSupabase(); // Use the service role client

    // Perform signInWithPassword using the service role client.
    // This will directly interact with Supabase Auth bypassing RLS (which is fine for auth ops).
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      console.error("Supabase signInWithPassword error:", error.message);
      return withCorsHeaders(
        new Response(JSON.stringify({ error: error.message }), {
          status: 401,
          headers: { "Content-Type": "application/json" },
        }),
      );
    }

    if (!data.session || !data.user) {
      return withCorsHeaders(
        new Response(
          JSON.stringify({
            error: "Authentication failed: No session or user data returned.",
          }),
          { status: 401, headers: { "Content-Type": "application/json" } },
        ),
      );
    }

    // Return the tokens and user data to the client
    return withCorsHeaders(
      new Response(
        JSON.stringify({
          user: {
            id: data.user.id,
            email: data.user.email,
            // Add any other user fields you want to expose safely
          },
          session: {
            access_token: data.session.access_token,
            refresh_token: data.session.refresh_token,
            expires_at: data.session.expires_at, // Provide expires_at for mobile to manage refresh logic
          },
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        },
      ),
    );
  } catch (err) {
    console.error("API /auth error:", err);
    return withCorsHeaders(
      new Response(JSON.stringify({ error: "Internal server error" }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }),
    );
  }
}
