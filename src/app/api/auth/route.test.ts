// @jest-environment node
import { POST } from "./route";
import { NextRequest } from "next/server";

jest.mock("@/utils/supabase/service-role", () => ({
  getServiceRoleSupabase: jest.fn(() => ({
    auth: {
      api: {
        getUser: jest.fn(() => ({
          user: { id: "user-id", email: "<EMAIL>" },
        })),
      },
    },
  })),
}));

describe("Auth API Route", () => {
  it("should return 400 if no access_token is provided", async () => {
    const req = {
      json: async () => ({}),
    } as unknown as NextRequest;
    const res = await POST(req);
    expect(res.status).toBe(400);
  });

  it("should return Password must be at least 6 characters", async () => {
    const req = {
      json: async () => ({ email: "<EMAIL>", password: "12345" }),
    } as unknown as NextRequest;
    const res = await POST(req);
    expect(res.status).toBe(400);
  });

  it("should return Email is required", async () => {
    const req = {
      json: async () => ({ password: "12345" }),
    } as unknown as NextRequest;
    const res = await POST(req);
    expect(res.status).toBe(400);
  });
});
