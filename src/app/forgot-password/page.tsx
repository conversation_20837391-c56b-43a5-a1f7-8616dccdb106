"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import ForgotPasswordPage, {
  PasswordResetStep,
} from "@/components/ForgotPasswordPage";
import { createClient } from "@/utils/supabase/client";

const ForgotPasswordPageContainer = () => {
  const router = useRouter();
  const supabase = createClient();

  const [email, setEmail] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState<PasswordResetStep>(
    PasswordResetStep.EMAIL_INPUT,
  );

  const handleSendOTP = async (email: string) => {
    setIsLoading(true);
    setError(null);

    const { error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        shouldCreateUser: false,
      },
    });

    if (error) {
      setError(error.message);
    } else {
      setEmail(email);
      setCurrentStep(PasswordResetStep.OTP_VERIFICATION);
    }
    setIsLoading(false);
  };

  const handleVerifyOTP = async (email: string, otp: string) => {
    setIsLoading(true);
    setError(null);

    const { error } = await supabase.auth.verifyOtp({
      email,
      token: otp,
      type: "email",
    });

    if (error) {
      setError(error.message);
    } else {
      setCurrentStep(PasswordResetStep.PASSWORD_RESET);
    }
    setIsLoading(false);
  };

  return (
    <ForgotPasswordPage
      currentStep={currentStep}
      email={email}
      isLoading={isLoading}
      errorMessage={error ?? undefined}
      onSendOTP={handleSendOTP}
      onVerifyOTP={handleVerifyOTP}
      onBackToLogin={() => router.push("/login")}
    />
  );
};

export default ForgotPasswordPageContainer;
