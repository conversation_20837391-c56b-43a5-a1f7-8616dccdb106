import { BasedEntities } from "../general";
import type { IVFScoreRow } from "@/types/ivf-score";

// Guest session data structure
export interface GuestSessionRow extends BasedEntities {
  session_token: string;
  ivf_data: IVFScoreRow;
  current_step: number;
  email?: string;
  is_verified: boolean;
  expires_at: Date;
}

export interface GuestSessionInsert extends BasedEntities {
  session_token: string;
  ivf_data: IVFScoreRow;
  current_step: number;
  email?: string;
  is_verified: boolean;
  expires_at: Date;
}

export interface GuestSessionUpdate extends BasedEntities {
  ivf_data?: IVFScoreRow;
  current_step?: number;
  email?: string;
  is_verified?: boolean;
  expires_at?: Date;
}
