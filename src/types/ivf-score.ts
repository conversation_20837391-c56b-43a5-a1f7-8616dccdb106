import type { ivf_scores as IVFScorePrisma } from "@/generated/prisma";

/**
 * Represents the structure of an IVF score row in the database.
 *
 * @remarks
 * This type alias is derived from the Prisma-generated `ivf_scores` type
 * to ensure consistency with the database schema. It omits database-specific
 * fields like `id`, `user_id`, `created_at`, and `updated_at` to represent
 * the core IVF data.
 *
 * @example
 * ```ts
 * import type { IVFScoreRow } from "@/types/ivf-score";
 *
 * const scoreData: IVFScoreRow = {
 *   age: 35,
 *   amh: 1.5,
 *   art_type: "IVF",
 *   stimulation_protocol: "Antagonist",
 *   embryo_transfer_day: 5,
 *   endometrial_thickness: 10,
 *   fsh: 8.2,
 *   lh: 5.1,
 *   prolactin: 15,
 *   e2: 2000,
 *   sperm_concentration: 40,
 *   sperm_motility: 50,
 *   sperm_morphology: 4,
 *   fertilization_method: "ICSI",
 *   number_of_oocytes_retrieved: 12,
 *   number_of_embryos_transferred: 2,
 *   previous_ivf_cycles: 1,
 *   previous_ivf_failures: 0,
 *   current_step: 4,
 * };
 * ```
 */
export type IVFScoreRow = Omit<
  IVFScorePrisma,
  "id" | "user_id" | "created_at" | "updated_at"
>;
