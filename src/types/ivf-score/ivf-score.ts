// Types for IVF Score calculation input
import { BasedEntities } from "@/types/general";

export interface IVFScoreRow extends BasedEntities{
  age: number;
  height: number;
  weight: number;
  menstrual_regularity: "regular" | "irregular";
  infertility_duration: string;
  ivf_attempts: number;
  known_conditions: string;
  stress_level: number;
  diet_type: "balanced" | "vegetarian" | "junk_heavy" | "skipping_meals";
  exercise_frequency: "daily" | "two_to_three_times" | "rarely_or_never";
  sleep_quality: number;
  emotional_support_at_home: boolean;
  smoking_or_alcohol_habits: string;
  household_income_range: "under_10k" | "from_10k_to_50k" | "from_50k_to_1l" | "above_1l";
  living_area: "urban" | "semi_urban" | "rural";
  work_stress_level: "low" | "medium" | "high";
  pollution_exposure: "high" | "moderate" | "low";
  occupation_type: "desk_job" | "field_work" | "night_shift" | "homemaker";
}


export interface IVFScoreInsert extends BasedEntities{
  age: number;
  height: number;
  weight: number;
  menstrual_regularity: "regular" | "irregular";
  infertility_duration: string;
  ivf_attempts: number;
  known_conditions: string;
  stress_level: number;
  diet_type: "balanced" | "vegetarian" | "junk_heavy" | "skipping_meals";
  exercise_frequency: "daily" | "two_to_three_times" | "rarely_or_never";
  sleep_quality: number;
  emotional_support_at_home: boolean;
  smoking_or_alcohol_habits: string;
  household_income_range: "under_10k" | "from_10k_to_50k" | "from_50k_to_1l" | "above_1l";
  living_area: "urban" | "semi_urban" | "rural";
  work_stress_level: "low" | "medium" | "high";
  pollution_exposure: "high" | "moderate" | "low";
  occupation_type: "desk_job" | "field_work" | "night_shift" | "homemaker";
}


export interface IVFScoreUpdate extends BasedEntities{
  age?: number;
  height?: number;
  weight?: number;
  menstrual_regularity?: "regular" | "irregular";
  infertility_duration?: string;
  ivf_attempts?: number;
  known_conditions?: string;
  stress_level?: number;
  diet_type?: "balanced" | "vegetarian" | "junk_heavy" | "skipping_meals";
  exercise_frequency?: "daily" | "two_to_three_times" | "rarely_or_never";
  sleep_quality?: number;
  emotional_support_at_home?: boolean;
  smoking_or_alcohol_habits?: string;
  household_income_range?: "under_10k" | "from_10k_to_50k" | "from_50k_to_1l" | "above_1l";
  living_area?: "urban" | "semi_urban" | "rural";
  work_stress_level?: "low" | "medium" | "high";
  pollution_exposure?: "high" | "moderate" | "low";
  occupation_type?: "desk_job" | "field_work" | "night_shift" | "homemaker";
}