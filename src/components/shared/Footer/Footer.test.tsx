import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import Footer from "./Footer";

describe("Footer Component", () => {
  describe("Rendering", () => {
    it("renders copyright text", () => {
      render(<Footer />);
      expect(screen.getByText("© 2025 Gunjan IVF")).toBeInTheDocument();
    });

    it("renders Privacy Policy link", () => {
      render(<Footer />);
      const privacyLink = screen.getByRole("link", { name: "Privacy Policy" });
      expect(privacyLink).toBeInTheDocument();
      expect(privacyLink).toHaveAttribute("href", "/privacy-policy");
    });

    it("renders Terms & Conditions link", () => {
      render(<Footer />);
      const termsLink = screen.getByRole("link", {
        name: "Terms & Conditions",
      });
      expect(termsLink).toBeInTheDocument();
      expect(termsLink).toHaveAttribute("href", "/terms-conditions");
    });

    it("applies custom className", () => {
      render(<Footer className="custom-footer" />);
      const footer = screen.getByRole("contentinfo");
      expect(footer).toHaveClass("custom-footer");
    });
  });

  describe("Accessibility", () => {
    it("has proper semantic structure", () => {
      render(<Footer />);
      const footer = screen.getByRole("contentinfo");
      expect(footer).toBeInTheDocument();
    });

    it("has accessible links", () => {
      render(<Footer />);
      const links = screen.getAllByRole("link");
      expect(links).toHaveLength(2);
      links.forEach((link) => {
        expect(link).toHaveAttribute("href");
      });
    });
  });

  describe("Styling", () => {
    it("has correct base classes", () => {
      render(<Footer />);
      const footer = screen.getByRole("contentinfo");
      expect(footer).toHaveClass(
        "w-full",
        "bg-[var(--red-1)]",
        "py-[1.875rem]",
        "px-6",
        "md:px-[9.375rem]",
        "h-[6.625rem]",
      );
    });

    it("merges custom className with base classes", () => {
      render(<Footer className="mt-8 border-t" />);
      const footer = screen.getByRole("contentinfo");
      expect(footer).toHaveClass(
        "w-full",
        "bg-[var(--red-1)]",
        "py-[1.875rem]",
        "px-6",
        "md:px-[9.375rem]",
        "h-[6.625rem]",
        "mt-8",
        "border-t",
      );
    });

    it("has horizontal link layout", () => {
      render(<Footer />);
      const linkContainer = screen
        .getByRole("contentinfo")
        .querySelector(
          "div:nth-child(1) > div:nth-child(1) > div:nth-child(2)",
        );
      expect(linkContainer).toHaveClass(
        "flex",
        "flex-row",
        "justify-start",
        "gap-6",
      );
    });
  });
});
