import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import Footer from "./Footer";

const meta: Meta<typeof Footer> = {
  title: "Components/Shared/Footer",
  component: Footer,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component:
          "A simple footer component with copyright information and legal links. Uses design system colors and typography.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    className: {
      control: { type: "text" },
      description: "Additional CSS classes to apply to the footer",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story: "The default footer with copyright and legal links.",
      },
    },
  },
};

export const InLayout: Story = {
  render: () => (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1 bg-white p-8">
        <div className="max-w-md mx-auto text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Page Content
          </h1>
          <p className="text-gray-600">
            This shows how the footer appears at the bottom of a page layout.
          </p>
        </div>
      </div>
      <Footer />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Footer component shown in a full page layout context.",
      },
    },
  },
};

export const ResponsiveDemo: Story = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-4 text-gray-900">
          Mobile View Simulation (px-6 = 24px)
        </h3>
        <div className="w-full max-w-sm mx-auto">
          <footer className="w-full h-[6.625rem] bg-[var(--pink-1)] py-[1.875rem] px-6">
            <div className="w-full">
              <div className="text-sm font-medium text-[var(--dark-grey)] space-y-1">
                <p>© 2025 Gunjan IVF</p>
                <div className="flex flex-row justify-start gap-6">
                  <a
                    href="/privacy-policy"
                    className="text-[var(--dark-grey)] hover:text-[var(--darker-grey)] transition-colors duration-200"
                  >
                    Privacy Policy
                  </a>
                  <a
                    href="/terms-conditions"
                    className="text-[var(--dark-grey)] hover:text-[var(--darker-grey)] transition-colors duration-200"
                  >
                    Terms & Conditions
                  </a>
                </div>
              </div>
            </div>
          </footer>
        </div>
        <p className="text-sm text-gray-600 mt-2">
          • Mobile horizontal padding: 24px (px-6)
          <br />
          • Fixed vertical padding: 30px
          <br />• Links arranged horizontally
          <br />• Full width of mobile screen (384px max)
        </p>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4 text-gray-900">
          Desktop View (px-[9.375rem] = 150px)
        </h3>
        <div className="w-full">
          <Footer />
        </div>
        <p className="text-sm text-gray-600 mt-2">
          • Desktop horizontal padding: 150px (md:px-[9.375rem])
          <br />
          • Fixed vertical padding: 30px
          <br />
          • Links arranged horizontally
          <br />• Full width of desktop screen
        </p>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates responsive behavior with explicit mobile and desktop padding. The mobile view shows the px-6 (24px) padding while desktop shows md:px-[9.375rem] (150px) padding.",
      },
    },
  },
};

export const MobileFullWidth: Story = {
  render: () => (
    <div className="w-full">
      <div className="mb-4 text-center">
        <p className="text-sm text-gray-600">
          This footer stretches to full width of the container/screen
        </p>
      </div>
      <Footer />
    </div>
  ),
  parameters: {
    layout: "fullscreen",
    viewport: {
      defaultViewport: "mobile1",
    },
    docs: {
      description: {
        story:
          "Footer shown at full width on mobile viewport. The footer automatically takes the full width of the screen with 24px (px-6) horizontal padding on mobile devices.",
      },
    },
  },
};
