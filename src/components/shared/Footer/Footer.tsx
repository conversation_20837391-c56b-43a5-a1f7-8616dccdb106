import React from "react";

export interface FooterProps {
  className?: string;
}

const Footer: React.FC<FooterProps> = ({ className = "" }) => {
  return (
    <footer
      className={`w-full h-[6.625rem] bg-[var(--red-1)] py-[1.875rem] px-6 md:px-[9.375rem] ${className}`}
    >
      <div className="w-full">
        <div className="text-sm font-medium text-[var(--grey-6)] space-y-1">
          <p>© 2025 Gunjan IVF</p>
          <div className="flex flex-row justify-start gap-6">
            <a
              href="/privacy-policy"
              className="text-[var(--grey-6)] hover:text-[var(--darker-grey)] transition-colors duration-200"
            >
              Privacy Policy
            </a>
            <a
              href="/terms-conditions"
              className="text-[var(--grey-6)] hover:text-[var(--darker-grey)] transition-colors duration-200"
            >
              Terms & Conditions
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
