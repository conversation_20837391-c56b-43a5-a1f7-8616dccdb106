import React from "react";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import ToggleButton from "./ToggleButton";

describe("ToggleButton", () => {
  const mockOnClick = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Component Rendering", () => {
    it("renders without crashing", () => {
      render(
        <ToggleButton isSelected={false} onClick={mockOnClick}>
          Test Button
        </ToggleButton>
      );
      expect(screen.getByText("Test Button")).toBeInTheDocument();
    });

    it("renders children correctly", () => {
      render(
        <ToggleButton isSelected={false} onClick={mockOnClick}>
          <span>Custom Content</span>
        </ToggleButton>
      );
      expect(screen.getByText("Custom Content")).toBeInTheDocument();
    });

    it("applies custom className", () => {
      render(
        <ToggleButton
          isSelected={false}
          onClick={mockOnClick}
          className="custom-class"
        >
          Test Button
        </ToggleButton>
      );
      expect(screen.getByText("Test Button")).toHaveClass("custom-class");
    });
  });

  describe("Selection States", () => {
    it("applies selected styles when isSelected is true", () => {
      render(
        <ToggleButton isSelected={true} onClick={mockOnClick}>
          Selected Button
        </ToggleButton>
      );
      const button = screen.getByText("Selected Button");
      expect(button).toHaveClass("bg-[var(--violet-6)]");
      expect(button).toHaveClass("text-white");
      expect(button).toHaveClass("font-bold");
    });

    it("applies unselected styles when isSelected is false", () => {
      render(
        <ToggleButton isSelected={false} onClick={mockOnClick}>
          Unselected Button
        </ToggleButton>
      );
      const button = screen.getByText("Unselected Button");
      expect(button).toHaveClass("border-[var(--grey-3)]");
      expect(button).toHaveClass("text-[var(--grey-6)]");
      expect(button).toHaveClass("font-medium");
    });
  });

  describe("Variants", () => {
    it("applies default variant styles", () => {
      render(
        <ToggleButton
          isSelected={false}
          onClick={mockOnClick}
          variant="default"
        >
          Default Button
        </ToggleButton>
      );
      const button = screen.getByText("Default Button");
      expect(button).toHaveClass("py-3");
      expect(button).toHaveClass("w-[8.438rem]");
      expect(button).toHaveClass("text-center");
    });

    it("applies compact variant styles", () => {
      render(
        <ToggleButton
          isSelected={false}
          onClick={mockOnClick}
          variant="compact"
        >
          Compact Button
        </ToggleButton>
      );
      const button = screen.getByText("Compact Button");
      expect(button).toHaveClass("px-6");
      expect(button).toHaveClass("py-3");
    });

    it("uses default variant when not specified", () => {
      render(
        <ToggleButton isSelected={false} onClick={mockOnClick}>
          Default Button
        </ToggleButton>
      );
      const button = screen.getByText("Default Button");
      expect(button).toHaveClass("w-[8.438rem]");
      expect(button).toHaveClass("text-center");
    });
  });

  describe("Disabled State", () => {
    it("applies disabled styles when disabled is true", () => {
      render(
        <ToggleButton isSelected={false} onClick={mockOnClick} disabled={true}>
          Disabled Button
        </ToggleButton>
      );
      const button = screen.getByText("Disabled Button");
      expect(button).toHaveClass("opacity-50");
      expect(button).toHaveClass("cursor-not-allowed");
      expect(button).toBeDisabled();
    });

    it("does not call onClick when disabled", async () => {
      const user = userEvent.setup();
      render(
        <ToggleButton isSelected={false} onClick={mockOnClick} disabled={true}>
          Disabled Button
        </ToggleButton>
      );
      const button = screen.getByText("Disabled Button");
      await user.click(button);
      expect(mockOnClick).not.toHaveBeenCalled();
    });

    it("applies enabled styles when disabled is false", () => {
      render(
        <ToggleButton isSelected={false} onClick={mockOnClick} disabled={false}>
          Enabled Button
        </ToggleButton>
      );
      const button = screen.getByText("Enabled Button");
      expect(button).toHaveClass("cursor-pointer");
      expect(button).not.toHaveClass("opacity-50");
      expect(button).not.toBeDisabled();
    });
  });

  describe("Interactions", () => {
    it("calls onClick when clicked", async () => {
      const user = userEvent.setup();
      render(
        <ToggleButton isSelected={false} onClick={mockOnClick}>
          Clickable Button
        </ToggleButton>
      );
      const button = screen.getByText("Clickable Button");
      await user.click(button);
      expect(mockOnClick).toHaveBeenCalledTimes(1);
    });

    it("calls onClick multiple times when clicked multiple times", async () => {
      const user = userEvent.setup();
      render(
        <ToggleButton isSelected={false} onClick={mockOnClick}>
          Clickable Button
        </ToggleButton>
      );
      const button = screen.getByText("Clickable Button");
      await user.click(button);
      await user.click(button);
      await user.click(button);
      expect(mockOnClick).toHaveBeenCalledTimes(3);
    });

    it("has proper button type", () => {
      render(
        <ToggleButton isSelected={false} onClick={mockOnClick}>
          Button
        </ToggleButton>
      );
      const button = screen.getByText("Button");
      expect(button).toHaveAttribute("type", "button");
    });
  });

  describe("Base Styles", () => {
    it("always applies base CSS classes", () => {
      render(
        <ToggleButton isSelected={false} onClick={mockOnClick}>
          Button
        </ToggleButton>
      );
      const button = screen.getByText("Button");
      expect(button).toHaveClass("transition-all");
      expect(button).toHaveClass("duration-200");
      expect(button).toHaveClass("rounded-[0.25rem]");
      expect(button).toHaveClass("font-medium");
    });
  });
});
