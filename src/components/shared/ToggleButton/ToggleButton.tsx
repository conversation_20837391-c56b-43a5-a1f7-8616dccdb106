import React from "react";

export interface ToggleButtonProps {
  children: React.ReactNode;
  isSelected: boolean;
  onClick: () => void;
  className?: string;
  disabled?: boolean;
  variant?: "default" | "compact";
}

const ToggleButton: React.FC<ToggleButtonProps> = ({
  children,
  isSelected,
  onClick,
  className = "",
  disabled = false,
  variant = "default",
}) => {
  const baseClasses =
    "transition-all duration-200 rounded-[0.25rem] font-medium";

  const variantClasses = {
    default: "py-3 w-[8.438rem] text-center text-base",
    compact: "px-6 py-3 text-base",
  };

  const stateClasses = isSelected
    ? "bg-[var(--violet-6)] border border-[var(--violet-8)] text-white font-bold"
    : "border border-[var(--grey-3)] text-[var(--grey-6)] hover:bg-[var(--grey-3)] font-medium";

  const disabledClasses = disabled
    ? "opacity-50 cursor-not-allowed"
    : "cursor-pointer";

  const combinedClasses = `${baseClasses} ${variantClasses[variant]} ${stateClasses} ${disabledClasses} ${className}`;

  return (
    <button
      type="button"
      onClick={onClick}
      disabled={disabled}
      className={combinedClasses}
    >
      {children}
    </button>
  );
};

export default ToggleButton;
