import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import ToggleButton from "./ToggleButton";

const meta: Meta<typeof ToggleButton> = {
  title: "Components/Shared/ToggleButton",
  component: ToggleButton,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A reusable toggle button component for selection states with multiple variants and styling options.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    children: {
      description: "The content to display inside the button",
      control: "text",
    },
    isSelected: {
      description: "Whether the button is in selected state",
      control: "boolean",
    },
    onClick: {
      description: "Function called when button is clicked",
      action: "clicked",
    },
    className: {
      description: "Additional CSS classes to apply",
      control: "text",
    },
    disabled: {
      description: "Whether the button is disabled",
      control: "boolean",
    },
    variant: {
      description: "Visual variant of the button",
      control: "select",
      options: ["default", "compact"],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: "Option 1",
    isSelected: false,
  },
};

export const Selected: Story = {
  args: {
    children: "Selected Option",
    isSelected: true,
  },
};

export const Compact: Story = {
  args: {
    children: "Regular",
    isSelected: false,
    variant: "compact",
  },
};

export const CompactSelected: Story = {
  args: {
    children: "Irregular",
    isSelected: true,
    variant: "compact",
  },
};

export const Disabled: Story = {
  args: {
    children: "Disabled Option",
    isSelected: false,
    disabled: true,
  },
};

export const DisabledSelected: Story = {
  args: {
    children: "Disabled Selected",
    isSelected: true,
    disabled: true,
  },
};

export const WithCustomClass: Story = {
  args: {
    children: "Custom Style",
    isSelected: false,
    className: "border-2 border-red-500",
  },
};

export const ButtonGroup: Story = {
  render: () => (
    <div className="flex gap-4">
      <ToggleButton isSelected={true} onClick={() => {}} variant="compact">
        Regular
      </ToggleButton>
      <ToggleButton isSelected={false} onClick={() => {}} variant="compact">
        Irregular
      </ToggleButton>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Example of ToggleButton used in a group for selection options.",
      },
    },
  },
};

export const DurationOptions: Story = {
  render: () => (
    <div className="flex gap-4 flex-wrap">
      <ToggleButton isSelected={true} onClick={() => {}}>
        &lt;6 months
      </ToggleButton>
      <ToggleButton isSelected={false} onClick={() => {}}>
        6—12 months
      </ToggleButton>
      <ToggleButton isSelected={false} onClick={() => {}}>
        &gt;12 months
      </ToggleButton>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Example of ToggleButton used for duration selection options.",
      },
    },
  },
};
