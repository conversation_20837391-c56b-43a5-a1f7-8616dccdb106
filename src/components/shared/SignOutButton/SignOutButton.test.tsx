import { render, screen, fireEvent } from "@testing-library/react";
import SignOutButton from "./SignOutButton";

describe("SignOutButton", () => {
  it("renders the button", () => {
    render(<SignOutButton onSignOut={() => {}} />);
    expect(
      screen.getByRole("button", { name: "Sign Out" }),
    ).toBeInTheDocument();
  });

  it("calls the onSignOut function when clicked", () => {
    const handleSignOut = jest.fn();
    render(<SignOutButton onSignOut={handleSignOut} />);
    fireEvent.click(screen.getByRole("button", { name: "Sign Out" }));
    expect(handleSignOut).toHaveBeenCalledTimes(1);
  });
});
