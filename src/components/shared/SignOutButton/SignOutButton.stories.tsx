import type { Meta, StoryObj } from "@storybook/nextjs";
import SignOutButton from "./SignOutButton";

const meta: Meta<typeof SignOutButton> = {
  title: "Components/Shared/SignOutButton",
  component: SignOutButton,
  tags: ["autodocs"],
  argTypes: {
    onSignOut: { action: "signed out" },
  },
};

export default meta;
type Story = StoryObj<typeof SignOutButton>;

export const Default: Story = {
  args: {
    onSignOut: () => alert("Signing out..."),
  },
};
