import React from "react";
import { render, screen } from "@testing-library/react";
import PageLoader from "./PageLoader";

// Mock CSS import
jest.mock("@/styles/spinner.css", () => ({}));

describe("PageLoader", () => {
  describe("Component Rendering", () => {
    it("renders without crashing", () => {
      render(<PageLoader />);
      expect(screen.getByRole("status")).toBeInTheDocument();
    });

    it("renders with default text", () => {
      render(<PageLoader />);
      expect(screen.getByText("Loading...")).toBeInTheDocument();
    });

    it("renders with custom text", () => {
      render(<PageLoader text="Please wait..." />);
      expect(screen.getByText("Please wait...")).toBeInTheDocument();
    });

    it("does not render text when enableText is false", () => {
      render(<PageLoader enableText={false} />);
      expect(screen.queryByText("Loading...")).not.toBeInTheDocument();
    });

    it("renders spinner element", () => {
      render(<PageLoader />);
      const spinner = screen
        .getByRole("status")
        .querySelector(".loading-spinner");
      expect(spinner).toBeInTheDocument();
    });
  });

  describe("Accessibility", () => {
    it("has proper role attribute", () => {
      render(<PageLoader />);
      expect(screen.getByRole("status")).toBeInTheDocument();
    });

    it("has proper aria-label", () => {
      render(<PageLoader />);
      expect(screen.getByLabelText("Loading content")).toBeInTheDocument();
    });

    it("has aria-hidden on spinner", () => {
      render(<PageLoader />);
      const spinner = screen
        .getByRole("status")
        .querySelector(".loading-spinner");
      expect(spinner).toHaveAttribute("aria-hidden", "true");
    });

    it("maintains accessibility with custom text", () => {
      render(<PageLoader text="Custom loading message" />);
      expect(screen.getByRole("status")).toBeInTheDocument();
      expect(screen.getByLabelText("Loading content")).toBeInTheDocument();
    });

    it("maintains accessibility when text is disabled", () => {
      render(<PageLoader enableText={false} />);
      expect(screen.getByRole("status")).toBeInTheDocument();
      expect(screen.getByLabelText("Loading content")).toBeInTheDocument();
    });
  });

  describe("Styling and Layout", () => {
    it("applies default className", () => {
      const { container } = render(<PageLoader />);
      const loader = container.firstChild as HTMLElement;
      expect(loader).toHaveClass(
        "flex",
        "flex-col",
        "h-screen",
        "justify-center",
        "items-center",
        "w-full"
      );
    });

    it("applies custom className", () => {
      const { container } = render(<PageLoader className="custom-loader" />);
      const loader = container.firstChild as HTMLElement;
      expect(loader).toHaveClass(
        "flex",
        "flex-col",
        "h-screen",
        "justify-center",
        "items-center",
        "w-full",
        "custom-loader"
      );
    });

    it("applies custom props", () => {
      const { container } = render(<PageLoader data-testid="custom-loader" />);
      const loader = container.firstChild as HTMLElement;
      expect(loader).toHaveAttribute("data-testid", "custom-loader");
    });
  });

  describe("Props Handling", () => {
    it("handles all HTML attributes", () => {
      const { container } = render(
        <PageLoader
          id="test-loader"
          data-testid="test-loader"
          style={{ backgroundColor: "red" }}
        />
      );
      const loader = container.firstChild as HTMLElement;
      expect(loader).toHaveAttribute("id", "test-loader");
      expect(loader).toHaveAttribute("data-testid", "test-loader");
      expect(loader).toHaveStyle("background-color: rgb(255, 0, 0)");
    });

    it("handles onClick and other event handlers", () => {
      const handleClick = jest.fn();
      render(<PageLoader onClick={handleClick} />);
      const loader = screen.getByRole("status");

      loader.click();
      expect(handleClick).toHaveBeenCalledTimes(1);
    });
  });

  describe("Edge Cases", () => {
    it("handles empty text", () => {
      render(<PageLoader text="" />);
      expect(screen.getByRole("status")).toBeInTheDocument();
    });

    it("handles very long text", () => {
      const longText =
        "This is a very long loading message that might wrap to multiple lines and should still be accessible and properly displayed";
      render(<PageLoader text={longText} />);
      expect(screen.getByText(longText)).toBeInTheDocument();
    });

    it("handles special characters in text", () => {
      const specialText = "Loading... 🚀 100%";
      render(<PageLoader text={specialText} />);
      expect(screen.getByText(specialText)).toBeInTheDocument();
    });
  });

  describe("Component Structure", () => {
    it("has correct DOM structure", () => {
      const { container } = render(<PageLoader />);
      const loader = container.firstChild as HTMLElement;

      expect(loader.tagName).toBe("DIV");
      expect(loader.children).toHaveLength(2); // spinner + text div

      const spinner = loader.querySelector(".loading-spinner");
      const textDiv = loader.querySelector("div");

      expect(spinner).toBeInTheDocument();
      expect(textDiv).toBeInTheDocument();
    });

    it("maintains structure when text is disabled", () => {
      const { container } = render(<PageLoader enableText={false} />);
      const loader = container.firstChild as HTMLElement;

      expect(loader.children).toHaveLength(2); // still has both elements
      const textDiv = loader.querySelector("div");
      expect(textDiv).toBeInTheDocument();
      expect(textDiv?.textContent).toBe(""); // but text is empty
    });
  });
});
