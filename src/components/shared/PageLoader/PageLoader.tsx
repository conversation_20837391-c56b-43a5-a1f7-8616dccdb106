import "@/styles/spinner.css";

interface LoaderProps extends React.HTMLAttributes<HTMLDivElement> {
  text?: string;
  enableText?: boolean;
}

const PageLoader = ({
  text = "Loading...",
  enableText = true,
}: LoaderProps) => {
  return (
    <div className="flex flex-col h-screen justify-center items-center w-full">
      <span className="loading-spinner"></span>
      <div>{enableText && text}</div>
    </div>
  );
};

export default PageLoader;
