import "@/styles/spinner.css";

interface LoaderProps extends React.HTMLAttributes<HTMLDivElement> {
  text?: string;
  enableText?: boolean;
}

const PageLoader = ({
  text = "Loading...",
  enableText = true,
  className = "",
  ...props
}: LoaderProps) => {
  return (
    <div
      className={`flex flex-col h-screen justify-center items-center w-full ${className}`}
      role="status"
      aria-label="Loading content"
      {...props}
    >
      <span className="loading-spinner" aria-hidden="true"></span>
      <div>{enableText && text}</div>
    </div>
  );
};

export default PageLoader;
