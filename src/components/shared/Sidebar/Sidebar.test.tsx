import { render, screen, fireEvent } from "@testing-library/react";
import { LayoutGrid, Target } from "lucide-react";
import Sidebar from "./Sidebar";

// Mock next/image
jest.mock("next/image", () => ({
  __esModule: true,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @next/next/no-img-element
  default: (props: any) => <img alt={props.alt || ""} {...props} />,
}));

describe("Sidebar", () => {
  const mockItems = [
    {
      icon: <LayoutGrid />,
      title: "Dashboard",
      onClick: jest.fn(),
    },
    {
      icon: <Target />,
      title: "IVF Success Score",
      onClick: jest.fn(),
    },
  ];

  it("renders all sidebar items", () => {
    render(<Sidebar items={mockItems} />);
    expect(screen.getByText("Dashboard")).toBeInTheDocument();
    expect(screen.getByText("IVF Success Score")).toBeInTheDocument();
  });

  it("shows logo when expanded", () => {
    render(<Sidebar items={mockItems} />);
    expect(screen.getByAltText("GIVF Logo")).toBeInTheDocument();
  });

  it("collapses and expands when toggle button is clicked", () => {
    render(<Sidebar items={mockItems} />);

    // Initially expanded
    expect(screen.getByText("Dashboard")).toBeInTheDocument();

    // Click to collapse
    fireEvent.click(screen.getByLabelText("Collapse sidebar"));
    expect(screen.queryByText("Dashboard")).not.toBeInTheDocument();
    expect(screen.queryByAltText("GIVF Logo")).not.toBeInTheDocument();

    // Click to expand
    fireEvent.click(screen.getByLabelText("Expand sidebar"));
    expect(screen.getByText("Dashboard")).toBeInTheDocument();
    expect(screen.getByAltText("GIVF Logo")).toBeInTheDocument();
  });

  it("highlights the selected item", () => {
    render(<Sidebar items={mockItems} selectedItemIndex={1} />);
    const items = screen.getAllByRole("button");
    // Skip the collapse button which is also a button
    expect(items[1]).not.toHaveClass("bg-[var(--red-1)]");
    expect(items[2]).toHaveClass("bg-[var(--red-1)]");
  });

  it("calls onClick handler when item is clicked", () => {
    render(<Sidebar items={mockItems} />);
    fireEvent.click(screen.getByText("Dashboard"));
    expect(mockItems[0].onClick).toHaveBeenCalledTimes(1);
  });
});
