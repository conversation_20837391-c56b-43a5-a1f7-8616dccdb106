import React, { useState } from "react";
import SidebarItem from "./SidebarItem";
import { Settings, LifeBuoy } from "lucide-react";
import Image from "next/image";

export interface SidebarProps {
  items: {
    icon: React.ReactNode;
    title: string;
    onClick: () => void;
  }[];
  selectedItemIndex?: number;
}

const Sidebar: React.FC<SidebarProps> = ({ items, selectedItemIndex = 0 }) => {
  const [isCollapsed, setIsCollapsed] = useState(false);

  return (
    <div
      className={`
        flex flex-col h-screen bg-white shadow-lg transition-all duration-300 pt-6 pb-8
        ${isCollapsed ? "w-[6rem]" : "w-64"}
      `}
    >
      {/* Logo Section */}
      <div
        className={`flex items-center ${isCollapsed ? "justify-center" : "justify-between gap-8"}`}
      >
        {!isCollapsed && (
          <div className="pl-[1.25rem]">
            <Image
              src="/assets/givfLogo.png"
              alt="GIVF Logo"
              width={120}
              height={40}
              className="object-contain"
            />
          </div>
        )}
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="p-2 cursor-pointer rounded-sm"
          aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          <Image
            src="/assets/menu.svg"
            alt="Toggle sidebar"
            width={24}
            height={24}
            className={`transition-transform duration-300 ${isCollapsed ? "rotate-180" : ""}`}
          />
        </button>
      </div>

      {/* Main Navigation Items */}
      <nav className="flex-1 overflow-y-auto py-6 px-[1.25rem]">
        <div className="space-y-2">
          {items.map((item, index) => (
            <SidebarItem
              key={item.title}
              icon={item.icon}
              title={isCollapsed ? "" : item.title}
              isSelected={index === selectedItemIndex}
              onClick={item.onClick}
            />
          ))}
        </div>
      </nav>

      {/* Fixed Bottom Items */}
      <div className="px-[1.25rem] py-6 mt-auto border-t border-[var(--grey-3)]">
        <div className="space-y-2">
          <SidebarItem
            icon={<Settings />}
            title={isCollapsed ? "" : "Setting"}
            isSelected={false}
            onClick={() => console.log("Settings clicked")}
          />
          <SidebarItem
            icon={<LifeBuoy />}
            title={isCollapsed ? "" : "Help & Support"}
            isSelected={false}
            onClick={() => console.log("Help & Support clicked")}
          />
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
