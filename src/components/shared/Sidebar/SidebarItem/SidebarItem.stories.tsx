import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import SidebarItem from "./SidebarItem";
import {
  LayoutGrid,
  Target,
  Palmtree,
  PersonStanding,
  Utensils,
  Baby,
  HandHeart,
  Users,
  Settings,
  LifeBuoy,
  Bell,
  LogOut,
} from "lucide-react";

const meta: Meta<typeof SidebarItem> = {
  title: "Components/SidebarItem",
  component: SidebarItem,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A sidebar item component that can be used in navigation menus. Features selected and unselected states with icon and text.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    isSelected: {
      control: "boolean",
      description: "Whether the item is currently selected",
    },
    icon: {
      control: "object",
      description: "Icon component to display",
    },
    title: {
      control: "text",
      description: "Text to display next to the icon",
    },
    onClick: {
      action: "clicked",
      description: "Callback function when the item is clicked",
    },
  },
};

export default meta;
type Story = StoryObj<typeof SidebarItem>;

export const Selected: Story = {
  args: {
    isSelected: true,
    icon: <LayoutGrid />,
    title: "Dashboard",
    onClick: () => console.log("Dashboard clicked"),
  },
};

export const Unselected: Story = {
  args: {
    isSelected: false,
    icon: <LayoutGrid />,
    title: "Dashboard",
    onClick: () => console.log("Dashboard clicked"),
  },
};

export const AllSidebarItems: Story = {
  render: () => (
    <div className="w-64 bg-white shadow-lg p-4 space-y-2">
      <SidebarItem
        isSelected={true}
        icon={<LayoutGrid />}
        title="Dashboard"
        onClick={() => {}}
      />
      <SidebarItem
        isSelected={false}
        icon={<Target />}
        title="IVF Success Score"
        onClick={() => {}}
      />
      <SidebarItem
        isSelected={false}
        icon={<Palmtree />}
        title="IVF Journey"
        onClick={() => {}}
      />
      <SidebarItem
        isSelected={false}
        icon={<PersonStanding />}
        title="Wellness & Lifestyle"
        onClick={() => {}}
      />
      <SidebarItem
        isSelected={false}
        icon={<Utensils />}
        title="Fertility Diet Plan"
        onClick={() => {}}
      />
      <SidebarItem
        isSelected={false}
        icon={<Baby />}
        title="Good News Wall"
        onClick={() => {}}
      />
      <SidebarItem
        isSelected={false}
        icon={<HandHeart />}
        title="Period Tracker"
        onClick={() => {}}
      />
      <SidebarItem
        isSelected={false}
        icon={<Users />}
        title="Community"
        onClick={() => {}}
      />
      <SidebarItem
        isSelected={false}
        icon={<Settings />}
        title="Setting"
        onClick={() => {}}
      />
      <SidebarItem
        isSelected={false}
        icon={<LifeBuoy />}
        title="Help & Support"
        onClick={() => {}}
      />
      <SidebarItem
        isSelected={false}
        icon={<Bell />}
        title="Notification"
        onClick={() => {}}
      />
      <SidebarItem
        isSelected={false}
        icon={<LogOut />}
        title="Logout"
        onClick={() => {}}
      />
    </div>
  ),
};

export const SingleItemInSidebar: Story = {
  render: (args) => (
    <div className="w-64 bg-white shadow-lg">
      <SidebarItem {...args} />
    </div>
  ),
  args: {
    isSelected: false,
    icon: <LayoutGrid />,
    title: "Dashboard",
    onClick: () => console.log("Dashboard clicked"),
  },
};
