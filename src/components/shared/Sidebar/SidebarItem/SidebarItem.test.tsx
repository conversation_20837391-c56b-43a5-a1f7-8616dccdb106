import { render, screen, fireEvent } from "@testing-library/react";
import { LayoutGrid } from "lucide-react";
import SidebarItem from "./SidebarItem";

describe("SidebarItem", () => {
  const defaultProps = {
    isSelected: false,
    icon: <LayoutGrid />,
    title: "Dashboard",
    onClick: jest.fn(),
  };

  it("renders with the correct title", () => {
    render(<SidebarItem {...defaultProps} />);
    expect(screen.getByText("Dashboard")).toBeInTheDocument();
  });

  it("renders the icon", () => {
    render(<SidebarItem {...defaultProps} />);
    // Check if icon wrapper is present
    expect(screen.getByText("Dashboard").previousSibling).toHaveClass(
      "w-5 h-5",
    );
  });

  it("calls onClick when clicked", () => {
    render(<SidebarItem {...defaultProps} />);
    fireEvent.click(screen.getByRole("button"));
    expect(defaultProps.onClick).toHaveBeenCalledTimes(1);
  });

  it("applies selected styles when isSelected is true", () => {
    render(<SidebarItem {...defaultProps} isSelected={true} />);
    expect(screen.getByRole("button")).toHaveClass("bg-[var(--red-1)]");
    expect(screen.getByRole("button")).toHaveClass("text-[var(--red-6)]");
  });

  it("applies unselected styles when isSelected is false", () => {
    render(<SidebarItem {...defaultProps} />);
    expect(screen.getByRole("button")).toHaveClass("text-[var(--grey-6)]");
    expect(screen.getByRole("button")).toHaveClass("hover:bg-[var(--grey-3)]");
  });

  it("maintains consistent layout with different title lengths", () => {
    const { rerender } = render(<SidebarItem {...defaultProps} />);
    const shortButton = screen.getByRole("button");
    expect(shortButton).toHaveClass("flex items-center gap-3");

    rerender(
      <SidebarItem {...defaultProps} title="A Very Long Dashboard Title" />,
    );
    const longButton = screen.getByRole("button");
    expect(longButton).toHaveClass("flex items-center gap-3");
  });
});
