import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import <PERSON><PERSON>el<PERSON> from "./NeedHelp";

const meta: Meta<typeof NeedHelp> = {
  title: "Components/Shared/NeedHelp",
  component: NeedHelp,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A NeedHelp component that displays a help message with a clickable phone number. By default opens the phone dialer, but can be customized with a callback function.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    phoneNumber: {
      control: { type: "text" },
      description: "The phone number to display and dial",
    },
    onPhoneClick: {
      action: "phone clicked",
      description: "Callback function when phone number is clicked",
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS classes to apply to the component",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story: "The default NeedHelp component with the standard phone number.",
      },
    },
  },
};

export const CustomPhoneNumber: Story = {
  args: {
    phoneNumber: "******-123-4567",
  },
  parameters: {
    docs: {
      description: {
        story: "NeedHelp component with a custom phone number.",
      },
    },
  },
};

export const WithCustomHandler: Story = {
  args: {
    onPhoneClick: (phoneNumber: string) => alert(`Calling ${phoneNumber}...`),
  },
  parameters: {
    docs: {
      description: {
        story:
          "NeedHelp component with a custom click handler instead of default phone dialer.",
      },
    },
  },
};

export const InternationalNumbers: Story = {
  render: () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2 text-gray-900">India</h3>
        <NeedHelp phoneNumber="+91-**********" />
      </div>
      <div>
        <h3 className="text-lg font-semibold mb-2 text-gray-900">USA</h3>
        <NeedHelp phoneNumber="******-123-4567" />
      </div>
      <div>
        <h3 className="text-lg font-semibold mb-2 text-gray-900">UK</h3>
        <NeedHelp phoneNumber="+44-20-1234-5678" />
      </div>
      <div>
        <h3 className="text-lg font-semibold mb-2 text-gray-900">Australia</h3>
        <NeedHelp phoneNumber="+61-2-9876-5432" />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Examples of NeedHelp component with different international phone number formats.",
      },
    },
  },
};

export const WithCustomStyling: Story = {
  args: {
    className: "bg-gray-50 p-4 rounded-lg border border-gray-200",
  },
  parameters: {
    docs: {
      description: {
        story: "NeedHelp component with custom styling applied via className.",
      },
    },
  },
};

export const InPageLayout: Story = {
  render: (args) => (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
          Contact Support
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 text-center">
            <h3 className="font-semibold text-gray-900 mb-4">Email Support</h3>
            <p className="text-gray-600 text-sm mb-4">
              Get help via email within 24 hours
            </p>
            <button className="text-blue-600 hover:text-blue-800 font-medium">
              <EMAIL>
            </button>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 text-center">
            <h3 className="font-semibold text-gray-900 mb-4">Live Chat</h3>
            <p className="text-gray-600 text-sm mb-4">
              Chat with our support team instantly
            </p>
            <button className="text-green-600 hover:text-green-800 font-medium">
              Start Chat
            </button>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h3 className="font-semibold text-gray-900 mb-4 text-center">
              Phone Support
            </h3>
            <p className="text-gray-600 text-sm mb-4 text-center">
              Speak directly with our team
            </p>
            <NeedHelp {...args} />
          </div>
        </div>

        <div className="text-center">
          <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Emergency Support
            </h2>
            <p className="text-gray-600 mb-6">
              For urgent medical queries, please call our 24/7 helpline
            </p>
            <NeedHelp
              phoneNumber="+91-**********"
              onPhoneClick={(phone) =>
                console.log(`Emergency call to ${phone}`)
              }
            />
          </div>
        </div>
      </div>
    </div>
  ),
  args: {
    onPhoneClick: (phoneNumber: string) =>
      console.log(`Calling ${phoneNumber}`),
  },
  parameters: {
    docs: {
      description: {
        story:
          "NeedHelp component shown in a complete page layout context with other support options.",
      },
    },
  },
};

export const ResponsiveDemo: Story = {
  render: (args) => (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-4 text-gray-900">
          Mobile View (up to 639px)
        </h3>
        <div className="max-w-sm border border-gray-200 rounded p-4 bg-white">
          <NeedHelp {...args} />
        </div>
        <p className="text-sm text-gray-600 mt-2">
          • Centered text layout
          <br />
          • Touch-friendly button sizing
          <br />• Clear visual hierarchy
        </p>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4 text-gray-900">
          Desktop View (≥ 640px)
        </h3>
        <div className="border border-gray-200 rounded p-8 bg-white">
          <NeedHelp {...args} />
        </div>
        <p className="text-sm text-gray-600 mt-2">
          • Consistent centered layout
          <br />
          • Hover effects on desktop
          <br />• Accessible focus states
        </p>
      </div>
    </div>
  ),
  args: {
    onPhoneClick: (phoneNumber: string) =>
      console.log(`Calling ${phoneNumber}`),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates how the NeedHelp component appears across different screen sizes with consistent centering and appropriate spacing.",
      },
    },
  },
};
