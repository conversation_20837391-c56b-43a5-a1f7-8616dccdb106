import React from "react";

export interface NeedHelpProps {
  phoneNumber?: string;
  onPhoneClick?: (phoneNumber: string) => void;
  className?: string;
}

const NeedHelp: React.FC<NeedHelpProps> = ({
  phoneNumber = "+91-9990044555",
  onPhoneClick,
  className = "",
}) => {
  const handlePhoneClick = () => {
    if (onPhoneClick) {
      onPhoneClick(phoneNumber);
    } else {
      // Default behavior: open phone dialer
      window.location.href = `tel:${phoneNumber}`;
    }
  };

  return (
    <div className={`text-left ${className} flex flex-col items-start`}>
      <p className="text-[var(--grey-5)] text-base mb-2">Need Help?</p>
      <button
        type="button"
        onClick={handlePhoneClick}
        className="text-[var(--grey-7)] text-left text-lg md:text-xl font-medium hover:text-[var(--red-6)] transition-colors duration-200 rounded py-1"
      >
        {phoneNumber}
      </button>
    </div>
  );
};

export default NeedHelp;
