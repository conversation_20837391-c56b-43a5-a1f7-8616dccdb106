import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import Button, { ButtonType } from "./Button";

// Mock icon for testing
const MockIcon = () => <span data-testid="mock-icon">📧</span>;

describe("Button Component", () => {
  const mockOnClick = jest.fn();

  beforeEach(() => {
    mockOnClick.mockClear();
  });

  describe("Rendering", () => {
    it("renders with text", () => {
      render(
        <Button
          type={ButtonType.PRIMARY}
          text="Click me"
          onClick={mockOnClick}
        />,
      );
      expect(
        screen.getByRole("button", { name: "Click me" }),
      ).toBeInTheDocument();
    });

    it("renders primary button with correct styles", () => {
      render(
        <Button
          type={ButtonType.PRIMARY}
          text="Primary"
          onClick={mockOnClick}
        />,
      );
      const button = screen.getByRole("button");
      expect(button).toHaveClass(
        "bg-[var(--red-6)]",
        "text-white",
        "rounded-none",
      );
    });

    it("renders secondary button with correct styles", () => {
      render(
        <Button
          type={ButtonType.SECONDARY}
          text="Secondary"
          onClick={mockOnClick}
        />,
      );
      const button = screen.getByRole("button");
      expect(button).toHaveClass("bg-white", "border", "rounded-none");
    });

    it("renders with icon when provided", () => {
      render(
        <Button
          type={ButtonType.PRIMARY}
          text="With Icon"
          icon={<MockIcon />}
          onClick={mockOnClick}
        />,
      );
      expect(screen.getByTestId("mock-icon")).toBeInTheDocument();
      expect(screen.getByText("With Icon")).toBeInTheDocument();
    });

    it("renders without icon when not provided", () => {
      render(
        <Button
          type={ButtonType.PRIMARY}
          text="No Icon"
          onClick={mockOnClick}
        />,
      );
      expect(screen.queryByTestId("mock-icon")).not.toBeInTheDocument();
      expect(screen.getByText("No Icon")).toBeInTheDocument();
    });
  });

  describe("Interactions", () => {
    it("calls onClick when clicked", () => {
      render(
        <Button
          type={ButtonType.PRIMARY}
          text="Click me"
          onClick={mockOnClick}
        />,
      );
      const button = screen.getByRole("button");

      fireEvent.click(button);
      expect(mockOnClick).toHaveBeenCalledTimes(1);
    });

    it("does not call onClick when disabled", () => {
      render(
        <Button
          type={ButtonType.PRIMARY}
          text="Disabled"
          onClick={mockOnClick}
          disabled
        />,
      );
      const button = screen.getByRole("button");

      fireEvent.click(button);
      expect(mockOnClick).not.toHaveBeenCalled();
    });

    it("handles multiple clicks", () => {
      render(
        <Button
          type={ButtonType.PRIMARY}
          text="Click me"
          onClick={mockOnClick}
        />,
      );
      const button = screen.getByRole("button");

      fireEvent.click(button);
      fireEvent.click(button);
      fireEvent.click(button);

      expect(mockOnClick).toHaveBeenCalledTimes(3);
    });
  });

  describe("Disabled state", () => {
    it("applies disabled class when disabled", () => {
      render(
        <Button
          type={ButtonType.PRIMARY}
          text="Disabled"
          onClick={mockOnClick}
          disabled
        />,
      );
      const button = screen.getByRole("button");
      expect(button).toHaveClass(
        "disabled:opacity-50",
        "disabled:cursor-not-allowed",
      );
      expect(button).toBeDisabled();
    });

    it("does not apply disabled class when not disabled", () => {
      render(
        <Button
          type={ButtonType.PRIMARY}
          text="Enabled"
          onClick={mockOnClick}
        />,
      );
      const button = screen.getByRole("button");
      expect(button).toHaveClass(
        "disabled:opacity-50",
        "disabled:cursor-not-allowed",
      );
      expect(button).not.toBeDisabled();
    });
  });

  describe("Accessibility", () => {
    it("has correct button type", () => {
      render(
        <Button
          type={ButtonType.PRIMARY}
          text="Button"
          onClick={mockOnClick}
        />,
      );
      const button = screen.getByRole("button");
      expect(button).toHaveAttribute("type", "button");
    });

    it("is focusable when not disabled", () => {
      render(
        <Button
          type={ButtonType.PRIMARY}
          text="Focusable"
          onClick={mockOnClick}
        />,
      );
      const button = screen.getByRole("button");
      button.focus();
      expect(button).toHaveFocus();
    });

    it("is not focusable when disabled", () => {
      render(
        <Button
          type={ButtonType.PRIMARY}
          text="Not Focusable"
          onClick={mockOnClick}
          disabled
        />,
      );
      const button = screen.getByRole("button");
      expect(button).toBeDisabled();
    });
  });

  describe("Custom className", () => {
    it("applies custom className", () => {
      render(
        <Button
          type={ButtonType.PRIMARY}
          text="Custom"
          onClick={mockOnClick}
          className="custom-class"
        />,
      );
      const button = screen.getByRole("button");
      expect(button).toHaveClass("custom-class");
    });

    it("maintains default classes with custom className", () => {
      render(
        <Button
          type={ButtonType.SECONDARY}
          text="Custom"
          onClick={mockOnClick}
          className="custom-class"
        />,
      );
      const button = screen.getByRole("button");
      expect(button).toHaveClass("bg-white", "border", "custom-class");
    });
  });

  describe("Props validation", () => {
    it("handles empty text gracefully", () => {
      render(
        <Button type={ButtonType.PRIMARY} text="" onClick={mockOnClick} />,
      );
      const button = screen.getByRole("button");
      expect(button).toBeInTheDocument();
    });

    it("handles long text", () => {
      const longText =
        "This is a very long button text that might wrap to multiple lines";
      render(
        <Button
          type={ButtonType.PRIMARY}
          text={longText}
          onClick={mockOnClick}
        />,
      );
      expect(screen.getByText(longText)).toBeInTheDocument();
    });
  });

  describe("Component structure", () => {
    it("has correct DOM structure with icon", () => {
      render(
        <Button
          type={ButtonType.PRIMARY}
          text="With Icon"
          icon={<MockIcon />}
          onClick={mockOnClick}
        />,
      );

      const button = screen.getByRole("button");
      const icon = screen.getByTestId("mock-icon");
      const text = screen.getByText("With Icon");

      // Check that icon comes before text
      expect(button).toContainElement(icon);
      expect(button).toContainElement(text);
    });

    it("has correct DOM structure without icon", () => {
      render(
        <Button
          type={ButtonType.PRIMARY}
          text="No Icon"
          onClick={mockOnClick}
        />,
      );

      const button = screen.getByRole("button");
      const text = screen.getByText("No Icon");

      expect(button).toContainElement(text);
      expect(screen.queryByTestId("mock-icon")).not.toBeInTheDocument();
    });
  });
});
