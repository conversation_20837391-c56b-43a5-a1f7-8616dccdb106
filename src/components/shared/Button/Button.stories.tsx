import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/nextjs";
import <PERSON><PERSON>, { ButtonType } from "./Button";

const meta: Meta<typeof Button> = {
  title: "Components/Button",
  component: Button,
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "A custom button component built with Tailwind CSS. Features full-width design, two variants (primary and secondary), optional icons, and multiple size options. Perfect for forms and call-to-action elements.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    type: {
      control: { type: "select" },
      options: [ButtonType.PRIMARY, ButtonType.SECONDARY],
      description: "The visual style variant of the button",
    },
    text: {
      control: { type: "text" },
      description: "The text content displayed on the button",
    },
    size: {
      control: { type: "select" },
      options: ["sm", "default", "lg"],
      description: "The size of the button",
    },
    disabled: {
      control: { type: "boolean" },
      description: "Whether the button is disabled",
    },
    onClick: {
      action: "clicked",
      description: "Function called when the button is clicked",
    },
  },
  decorators: [
    (Story) => (
      <div style={{ maxWidth: "400px", margin: "0 auto" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Primary Button Stories
export const Primary: Story = {
  args: {
    type: ButtonType.PRIMARY,
    text: "Login",
    onClick: () => console.log("Primary button clicked"),
  },
  parameters: {
    docs: {
      description: {
        story:
          "The primary button with your brand pink color. Perfect for main actions like login, submit, or confirm.",
      },
    },
  },
};

export const Secondary: Story = {
  args: {
    type: ButtonType.SECONDARY,
    text: "Login with OTP",
    onClick: () => console.log("Secondary button clicked"),
  },
  parameters: {
    docs: {
      description: {
        story:
          "The secondary button with white background and gray border. Ideal for alternative actions.",
      },
    },
  },
};

// Size Variants
export const Sizes: Story = {
  render: () => (
    <div className="space-y-4">
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-2">Small</h3>
        <Button
          type={ButtonType.PRIMARY}
          text="Small Button"
          size="sm"
          onClick={() => {}}
        />
      </div>
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-2">Default</h3>
        <Button
          type={ButtonType.PRIMARY}
          text="Default Button"
          size="default"
          onClick={() => {}}
        />
      </div>
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-2">Large</h3>
        <Button
          type={ButtonType.PRIMARY}
          text="Large Button"
          size="lg"
          onClick={() => {}}
        />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Available button sizes: small (sm), default, and large (lg).",
      },
    },
  },
};

// Disabled States
export const DisabledStates: Story = {
  render: () => (
    <div className="space-y-3">
      <Button
        type={ButtonType.PRIMARY}
        text="Disabled Primary"
        disabled
        onClick={() => {}}
      />
      <Button
        type={ButtonType.SECONDARY}
        text="Disabled Secondary"
        disabled
        onClick={() => {}}
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Disabled button states with reduced opacity and no hover effects.",
      },
    },
  },
};

// Login Form Example
export const LoginForm: Story = {
  render: () => (
    <div className="bg-white p-6 rounded-lg shadow-sm border max-w-md mx-auto">
      <h2 className="text-xl font-semibold text-gray-900 mb-4 text-center">
        Welcome Back
      </h2>
      <div className="space-y-3">
        <Button type={ButtonType.PRIMARY} text="Login" onClick={() => {}} />
        <Button
          type={ButtonType.SECONDARY}
          text="Login with OTP"
          onClick={() => {}}
        />
      </div>
      <div className="mt-4 text-center">
        <a href="#" className="text-sm text-gray-600 hover:text-gray-800">
          Forgot your password?
        </a>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Example of how the buttons would appear in a real login form interface.",
      },
    },
  },
};

// All Variants Showcase
export const AllVariants: Story = {
  render: () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-3">Button Types</h3>
        <div className="space-y-3">
          <Button
            type={ButtonType.PRIMARY}
            text="Primary Button"
            onClick={() => {}}
          />
          <Button
            type={ButtonType.SECONDARY}
            text="Secondary Button"
            onClick={() => {}}
          />
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-3">
          Different Sizes
        </h3>
        <div className="space-y-3">
          <Button
            type={ButtonType.PRIMARY}
            text="Small Button"
            size="sm"
            onClick={() => {}}
          />
          <Button
            type={ButtonType.PRIMARY}
            text="Default Button"
            size="default"
            onClick={() => {}}
          />
          <Button
            type={ButtonType.PRIMARY}
            text="Large Button"
            size="lg"
            onClick={() => {}}
          />
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-3">
          Disabled States
        </h3>
        <div className="space-y-3">
          <Button
            type={ButtonType.PRIMARY}
            text="Disabled Primary"
            disabled
            onClick={() => {}}
          />
          <Button
            type={ButtonType.SECONDARY}
            text="Disabled Secondary"
            disabled
            onClick={() => {}}
          />
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Complete overview of all button variants, sizes, and states available in the component.",
      },
    },
  },
};
