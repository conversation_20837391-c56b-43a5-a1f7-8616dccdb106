import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import { useState } from "react";
import Input from "./Input";

const meta: Meta<typeof Input> = {
  title: "Components/Shared/Input",
  component: Input,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A versatile input component that supports multiple types, variants, sizes, and states. Features proper accessibility, error handling, and consistent styling.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    type: {
      description: "The type of input",
      control: "select",
      options: ["text", "email", "tel", "number", "date", "password"],
    },
    value: {
      description: "The controlled value of the input",
      control: "text",
    },
    onChange: {
      description: "Callback function called when input value changes",
      action: "onChange",
    },
    placeholder: {
      description: "Placeholder text",
      control: "text",
    },
    label: {
      description: "Label text (optional)",
      control: "text",
    },
    disabled: {
      description: "Whether the input is disabled",
      control: "boolean",
    },
    required: {
      description: "Whether the input is required",
      control: "boolean",
    },
    error: {
      description: "Whether the input has an error state",
      control: "boolean",
    },
    errorMessage: {
      description: "Error message to display",
      control: "text",
    },
    variant: {
      description: "Visual variant of the input",
      control: "select",
      options: ["default", "compact"],
    },
    size: {
      description: "Size of the input",
      control: "select",
      options: ["sm", "default", "lg"],
    },
  },
};

export default meta;
type Story = StoryObj<typeof Input>;

// Interactive wrapper for controlled inputs
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const InteractiveInput = (args: any) => {
  const [value, setValue] = useState(args.value || "");
  return <Input {...args} value={value} onChange={setValue} />;
};

export const Default: Story = {
  render: InteractiveInput,
  args: {
    placeholder: "Enter text here...",
  },
  parameters: {
    docs: {
      description: {
        story: "Default input with placeholder text.",
      },
    },
  },
};

export const WithLabel: Story = {
  render: InteractiveInput,
  args: {
    label: "Full Name",
    placeholder: "Enter your full name",
  },
  parameters: {
    docs: {
      description: {
        story: "Input with a label for better accessibility.",
      },
    },
  },
};

export const Required: Story = {
  render: InteractiveInput,
  args: {
    label: "Email Address",
    placeholder: "Enter your email",
    type: "email",
    required: true,
  },
  parameters: {
    docs: {
      description: {
        story: "Required input with label and red asterisk indicator.",
      },
    },
  },
};

export const WithError: Story = {
  render: InteractiveInput,
  args: {
    label: "Email Address",
    placeholder: "Enter your email",
    type: "email",
    error: true,
    errorMessage: "Please enter a valid email address",
    value: "invalid-email",
  },
  parameters: {
    docs: {
      description: {
        story: "Input in error state with validation message.",
      },
    },
  },
};

export const Disabled: Story = {
  render: InteractiveInput,
  args: {
    label: "Username",
    placeholder: "Cannot edit this field",
    value: "john_doe",
    disabled: true,
  },
  parameters: {
    docs: {
      description: {
        story: "Disabled input that cannot be interacted with.",
      },
    },
  },
};

// Input Types
export const InputTypes: Story = {
  render: () => (
    <div className="space-y-4 w-80">
      <InteractiveInput
        type="text"
        label="Text Input"
        placeholder="Enter text"
      />
      <InteractiveInput
        type="email"
        label="Email Input"
        placeholder="Enter email"
      />
      <InteractiveInput
        type="tel"
        label="Phone Input"
        placeholder="Enter phone number"
      />
      <InteractiveInput
        type="number"
        label="Number Input"
        placeholder="Enter number"
      />
      <InteractiveInput
        type="password"
        label="Password Input"
        placeholder="Enter password"
      />
      <InteractiveInput type="date" label="Date Input" />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Showcase of all supported input types.",
      },
    },
  },
};

// Variants
export const Variants: Story = {
  render: () => (
    <div className="space-y-4 w-80">
      <InteractiveInput
        variant="default"
        label="Default Variant"
        placeholder="Default styling"
      />
      <InteractiveInput
        variant="compact"
        label="Compact Variant"
        placeholder="Compact styling"
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Different visual variants of the input component.",
      },
    },
  },
};

// Sizes
export const Sizes: Story = {
  render: () => (
    <div className="space-y-4 w-80">
      <InteractiveInput
        size="sm"
        label="Small Size"
        placeholder="Small input"
      />
      <InteractiveInput
        size="default"
        label="Default Size"
        placeholder="Default input"
      />
      <InteractiveInput
        size="lg"
        label="Large Size"
        placeholder="Large input"
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Different sizes available for the input component.",
      },
    },
  },
};

// Form Example
export const FormExample: Story = {
  render: () => {
    const [formData, setFormData] = useState({
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      age: "",
      birthDate: "",
    });

    const handleChange = (field: string) => (value: string) => {
      setFormData((prev) => ({ ...prev, [field]: value }));
    };

    return (
      <div className="space-y-4 w-80">
        <h3 className="text-lg font-semibold text-gray-900">
          Registration Form
        </h3>
        <Input
          label="First Name"
          placeholder="Enter first name"
          value={formData.firstName}
          onChange={handleChange("firstName")}
          required
        />
        <Input
          label="Last Name"
          placeholder="Enter last name"
          value={formData.lastName}
          onChange={handleChange("lastName")}
          required
        />
        <Input
          type="email"
          label="Email Address"
          placeholder="Enter email"
          value={formData.email}
          onChange={handleChange("email")}
          required
        />
        <Input
          type="tel"
          label="Phone Number"
          placeholder="Enter phone number"
          value={formData.phone}
          onChange={handleChange("phone")}
        />
        <Input
          type="number"
          label="Age"
          placeholder="Enter age"
          value={formData.age}
          onChange={handleChange("age")}
        />
        <Input
          type="date"
          label="Date of Birth"
          value={formData.birthDate}
          onChange={handleChange("birthDate")}
        />
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story:
          "Example form using multiple Input components with different types and labels.",
      },
    },
  },
};
