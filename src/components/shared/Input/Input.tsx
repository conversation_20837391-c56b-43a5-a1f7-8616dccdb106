import React from "react";

export interface InputProps {
  type?: "text" | "email" | "tel" | "number" | "date" | "password";
  value: string | number;
  onChange: (value: string) => void;
  placeholder?: string;
  label?: string;
  id?: string;
  disabled?: boolean;
  required?: boolean;
  error?: boolean;
  errorMessage?: string;
  className?: string;
  variant?: "default" | "compact";
  size?: "sm" | "default" | "lg";
}

const Input: React.FC<InputProps> = ({
  type = "text",
  value,
  onChange,
  placeholder,
  label,
  id,
  disabled = false,
  required = false,
  error = false,
  errorMessage,
  className = "",
  variant = "default",
  size = "default",
}) => {
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;

  const baseClasses =
    "w-full px-4 py-3 border rounded-sm text-[var(--grey-7)] text-base bg-white focus:outline-none focus:ring-2 transition-all duration-200";

  const variantClasses = {
    default:
      "border-[var(--grey-3)] focus:ring-[var(--violet-6)] focus:border-[var(--violet-6)]",
    compact:
      "border-[var(--grey-2)] focus:ring-[var(--violet-5)] focus:border-[var(--violet-5)]",
  };

  const sizeClasses = {
    sm: "h-10 text-sm",
    default: "h-[3.125rem] text-base font-bold",
    lg: "h-14 text-lg",
  };

  const stateClasses = error
    ? "border-red-300 focus:ring-red-500 focus:border-red-500"
    : variantClasses[variant];

  const disabledClasses = disabled
    ? "opacity-50 cursor-not-allowed bg-[var(--grey-1)]"
    : "";

  const placeholderClasses = "placeholder:text-[var(--grey-5)]";

  const combinedClasses = `${baseClasses} ${sizeClasses[size]} ${stateClasses} ${disabledClasses} ${placeholderClasses} ${className}`;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  };

  return (
    <div className="w-full">
      {label && (
        <label
          htmlFor={inputId}
          className="block text-[var(--grey-6)] text-base font-medium mb-2"
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}

      <input
        id={inputId}
        type={type}
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
        disabled={disabled}
        required={required}
        className={combinedClasses}
        style={type === "date" ? { colorScheme: "light" } : undefined}
        aria-invalid={error}
        aria-describedby={
          error && errorMessage ? `${inputId}-error` : undefined
        }
      />

      {error && errorMessage && (
        <p
          id={`${inputId}-error`}
          className="mt-1 text-sm text-red-600"
          role="alert"
        >
          {errorMessage}
        </p>
      )}
    </div>
  );
};

export default Input;
