import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import Header, { NavigationTarget, HeaderState } from "./Header";

const meta: Meta<typeof Header> = {
  title: "Components/Shared/Header",
  component: Header,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component:
          "A header component with logo and navigation functionality. Features responsive design with the GUNJAN IVF World logo, Help, and Login navigation buttons.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    navigateTo: {
      action: "navigated",
      description: "Callback function when navigation buttons are clicked",
    },
    onClick: {
      action: "clicked",
      description: "Legacy callback function for action button",
    },
    state: {
      control: { type: "select" },
      options: [HeaderState.LOGIN, HeaderState.HELP],
      description: "Header state determining which text and button to show",
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS classes to apply to the header",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story: "The default header with logo and login button.",
      },
    },
  },
};

export const LoginState: Story = {
  args: {
    navigateTo: (target: NavigationTarget) => alert(`Navigating to ${target}!`),
    state: HeaderState.LOGIN,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Header in login state showing 'Have an account?' text with Login button.",
      },
    },
  },
};

export const HelpState: Story = {
  args: {
    navigateTo: (target: NavigationTarget) => alert(`Navigating to ${target}!`),
    state: HeaderState.HELP,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Header in help state showing 'Having Trouble?' text with Get Help button.",
      },
    },
  },
};

export const WithLoginAction: Story = {
  args: {
    onClick: () => alert("Login clicked!"),
  },
  parameters: {
    docs: {
      description: {
        story: "Header with legacy login click handler in default login state.",
      },
    },
  },
};

export const InPageLayout: Story = {
  render: (args) => (
    <div className="min-h-screen bg-gray-50">
      <Header {...args} />
      <div className="p-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Welcome to GUNJAN IVF World
          </h1>
          <p className="text-gray-600 text-lg">
            This shows how the header appears in a complete page layout with
            content below.
          </p>
          <div className="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((item) => (
              <div
                key={item}
                className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
              >
                <h3 className="font-semibold text-gray-900 mb-2">
                  Service {item}
                </h3>
                <p className="text-gray-600 text-sm">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  ),
  args: {
    navigateTo: (target: NavigationTarget) =>
      console.log(`Navigating to ${target}`),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Header component shown in a full page layout context with sample content.",
      },
    },
  },
};

export const ResponsiveDemo: Story = {
  render: (args) => (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-4 text-gray-900">
          Login State - Mobile View (up to 639px)
        </h3>
        <div className="max-w-sm border border-gray-200 rounded">
          <Header {...args} state={HeaderState.LOGIN} />
        </div>
        <p className="text-sm text-gray-600 mt-2">
          • Compact padding (24px)
          <br />
          • &quot;Have an account?&quot; text hidden on mobile
          <br />• Logo and Login button visible
        </p>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4 text-gray-900">
          Help State - Mobile View (up to 639px)
        </h3>
        <div className="max-w-sm border border-gray-200 rounded">
          <Header {...args} state={HeaderState.HELP} />
        </div>
        <p className="text-sm text-gray-600 mt-2">
          • Compact padding (24px)
          <br />
          • &quot;Having Trouble?&quot; text hidden on mobile
          <br />• Logo and Get Help button visible
        </p>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4 text-gray-900">
          Login State - Desktop View (≥ 1024px)
        </h3>
        <div className="border border-gray-200 rounded">
          <Header {...args} state={HeaderState.LOGIN} />
        </div>
        <p className="text-sm text-gray-600 mt-2">
          • Large horizontal padding (150px)
          <br />
          • Full &quot;Have an account?&quot; text visible
          <br />• Complete logo and Login navigation
        </p>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4 text-gray-900">
          Help State - Desktop View (≥ 1024px)
        </h3>
        <div className="border border-gray-200 rounded">
          <Header {...args} state={HeaderState.HELP} />
        </div>
        <p className="text-sm text-gray-600 mt-2">
          • Large horizontal padding (150px)
          <br />
          • Full &quot;Having Trouble?&quot; text visible
          <br />• Complete logo and Get Help navigation
        </p>
      </div>
    </div>
  ),
  args: {
    navigateTo: (target: NavigationTarget) =>
      console.log(`Navigating to ${target}`),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates responsive behavior and different states across screen sizes. The header adapts its padding and text visibility based on screen width, and shows different content based on state.",
      },
    },
  },
};
