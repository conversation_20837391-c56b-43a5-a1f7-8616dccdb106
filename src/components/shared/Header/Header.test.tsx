import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import Header, { NavigationTarget, HeaderState } from "./Header";

describe("Header Component", () => {
  describe("Rendering", () => {
    it("renders the logo image", () => {
      render(<Header />);
      const logo = screen.getByAltText("GUNJAN IVF World Logo");
      expect(logo).toBeInTheDocument();
      expect(logo).toHaveAttribute("src");
      expect(logo.getAttribute("src")).toContain("givfLogo.png");
    });

    it("renders the login button", () => {
      render(<Header />);
      const loginButton = screen.getByRole("button", { name: "Login" });
      expect(loginButton).toBeInTheDocument();
    });

    it("renders 'Have an account?' text", () => {
      render(<Header />);
      expect(screen.getByText("Have an account?")).toBeInTheDocument();
    });

    it("applies custom className", () => {
      render(<Header className="custom-header" />);
      const header = screen.getByRole("banner");
      expect(header).toHaveClass("custom-header");
    });
  });

  describe("Interactions", () => {
    it("calls onClick when login button is clicked", () => {
      const mockOnClick = jest.fn();
      render(<Header onClick={mockOnClick} />);

      const loginButton = screen.getByRole("button", { name: "Login" });
      fireEvent.click(loginButton);

      expect(mockOnClick).toHaveBeenCalledTimes(1);
    });

    it("does not call onClick when not provided", () => {
      // This should not throw an error
      render(<Header />);

      const loginButton = screen.getByRole("button", { name: "Login" });
      fireEvent.click(loginButton);

      // No error should be thrown
      expect(loginButton).toBeInTheDocument();
    });

    it("handles multiple login clicks", () => {
      const mockOnClick = jest.fn();
      render(<Header onClick={mockOnClick} />);

      const loginButton = screen.getByRole("button", { name: "Login" });

      fireEvent.click(loginButton);
      fireEvent.click(loginButton);
      fireEvent.click(loginButton);

      expect(mockOnClick).toHaveBeenCalledTimes(3);
    });
  });

  describe("Navigation", () => {
    it("calls navigateTo with LOGIN when login button is clicked", () => {
      const mockNavigateTo = jest.fn();
      render(<Header navigateTo={mockNavigateTo} />);

      const loginButton = screen.getByRole("button", { name: "Login" });
      fireEvent.click(loginButton);

      expect(mockNavigateTo).toHaveBeenCalledWith(NavigationTarget.LOGIN);
    });

    it("calls navigateTo with HELP when in help state", () => {
      const mockNavigateTo = jest.fn();
      render(<Header navigateTo={mockNavigateTo} state={HeaderState.HELP} />);

      const helpButton = screen.getByRole("button", { name: "Get Help" });
      fireEvent.click(helpButton);

      expect(mockNavigateTo).toHaveBeenCalledWith(NavigationTarget.HELP);
    });

    it("prioritizes navigateTo over onClick when both are provided", () => {
      const mockNavigateTo = jest.fn();
      const mockOnClick = jest.fn();
      render(<Header navigateTo={mockNavigateTo} onClick={mockOnClick} />);

      const loginButton = screen.getByRole("button", { name: "Login" });
      fireEvent.click(loginButton);

      expect(mockNavigateTo).toHaveBeenCalledWith(NavigationTarget.LOGIN);
      expect(mockOnClick).not.toHaveBeenCalled();
    });

    it("shows different states correctly", () => {
      const { rerender } = render(<Header />);
      expect(screen.getByText("Have an account?")).toBeInTheDocument();
      expect(screen.getByRole("button", { name: "Login" })).toBeInTheDocument();

      rerender(<Header state={HeaderState.HELP} />);
      expect(screen.getByText("Having Trouble?")).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: "Get Help" }),
      ).toBeInTheDocument();
    });

    it("handles navigation clicks in different states", () => {
      const mockNavigateTo = jest.fn();
      const { rerender } = render(<Header navigateTo={mockNavigateTo} />);

      const loginButton = screen.getByRole("button", { name: "Login" });
      fireEvent.click(loginButton);

      rerender(<Header navigateTo={mockNavigateTo} state={HeaderState.HELP} />);
      const helpButton = screen.getByRole("button", { name: "Get Help" });
      fireEvent.click(helpButton);

      expect(mockNavigateTo).toHaveBeenCalledTimes(2);
      expect(mockNavigateTo).toHaveBeenNthCalledWith(1, NavigationTarget.LOGIN);
      expect(mockNavigateTo).toHaveBeenNthCalledWith(2, NavigationTarget.HELP);
    });
  });

  describe("Accessibility", () => {
    it("has proper semantic structure", () => {
      render(<Header />);
      const header = screen.getByRole("banner");
      expect(header).toBeInTheDocument();
    });

    it("has accessible login button", () => {
      render(<Header />);
      const loginButton = screen.getByRole("button", { name: "Login" });
      expect(loginButton).toBeInTheDocument();
      expect(loginButton).toHaveAttribute("type", "button");
    });

    it("has proper logo alt text", () => {
      render(<Header />);
      const logo = screen.getByAltText("GUNJAN IVF World Logo");
      expect(logo).toBeInTheDocument();
    });

    it("login button is focusable", () => {
      render(<Header />);
      const loginButton = screen.getByRole("button", { name: "Login" });
      loginButton.focus();
      expect(loginButton).toHaveFocus();
    });

    it("login button has focus styles", () => {
      render(<Header />);
      const loginButton = screen.getByRole("button", { name: "Login" });
      expect(loginButton).toHaveClass(
        "text-[var(--red-6)]",
        "hover:text-[var(--red-7)]",
        "cursor-pointer",
        "font-medium",
      );
    });
  });

  describe("Styling", () => {
    it("has correct base classes", () => {
      render(<Header />);
      const header = screen.getByRole("banner");
      expect(header).toHaveClass(
        "w-full",
        "bg-white",
        "py-2",
        "md:py-4",
        "px-6",
        "md:px-[9.375rem]",
        "border-b",
        "border-[var(--grey-3)]",
      );
    });

    it("has correct logo styling", () => {
      render(<Header />);
      const logo = screen.getByAltText("GUNJAN IVF World Logo");
      expect(logo).toHaveClass(
        "h-auto",
        "max-h-[58px]",
        "md:h-[48px]",
        "w-auto",
        "max-w-[118px]",
        "md:max-w-[98px]",
      );
    });

    it("has correct login button styling", () => {
      render(<Header />);
      const loginButton = screen.getByRole("button", { name: "Login" });
      expect(loginButton).toHaveClass(
        "text-[var(--red-6)]",
        "hover:text-[var(--red-7)]",
        "cursor-pointer",
        "font-medium",
        "transition-colors",
        "duration-200",
      );
    });

    it("merges custom className with base classes", () => {
      render(<Header className="mt-4 shadow-lg" />);
      const header = screen.getByRole("banner");
      expect(header).toHaveClass(
        "w-full",
        "bg-white",
        "py-2",
        "md:py-4",
        "px-6",
        "md:px-[9.375rem]",
        "border-b",
        "border-[var(--grey-3)]",
        "mt-4",
        "shadow-lg",
      );
    });
  });

  describe("Responsive Behavior", () => {
    it("has responsive padding classes", () => {
      render(<Header />);
      const header = screen.getByRole("banner");
      expect(header).toHaveClass("px-6", "md:px-[9.375rem]");
    });

    it("has responsive text visibility", () => {
      const { rerender } = render(<Header />);
      const accountText = screen.getByText("Have an account?");
      expect(accountText).toHaveClass("text-[var(--dark-grey)]", "sm:inline");

      rerender(<Header state={HeaderState.HELP} />);
      const troubleText = screen.getByText("Having Trouble?");
      expect(troubleText).toHaveClass("text-[var(--dark-grey)]", "sm:inline");
    });
  });

  describe("Layout", () => {
    it("has correct flex layout", () => {
      render(<Header />);
      const headerContent = screen.getByRole("banner").firstChild;
      expect(headerContent).toHaveClass(
        "w-full",
        "flex",
        "items-center",
        "justify-between",
      );
    });

    it("has logo section with correct classes", () => {
      render(<Header />);
      const logoSection = screen.getByAltText(
        "GUNJAN IVF World Logo",
      ).parentElement;
      expect(logoSection).toHaveClass("flex", "items-center");
    });

    it("has navigation section with correct classes", () => {
      render(<Header />);
      const navigationSection =
        screen.getByText("Have an account?").parentElement;
      expect(navigationSection).toHaveClass(
        "flex",
        "flex-col",
        "items-end",
        "text-base",
      );
    });
  });
});
