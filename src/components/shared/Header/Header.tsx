import React from "react";
import Image from "next/image";

export enum NavigationTarget {
  LOGIN = "Login",
  HELP = "Help",
}

export enum HeaderState {
  LOGIN = "login",
  HELP = "help",
}

export interface HeaderProps {
  navigateTo?: (target: NavigationTarget) => void;
  onClick?: () => void;
  state?: HeaderState;
  className?: string;
}

const Header: React.FC<HeaderProps> = ({
  navigateTo,
  onClick,
  state = HeaderState.LOGIN,
  className = "",
}) => {
  const handleActionClick = () => {
    if (navigateTo) {
      if (state === HeaderState.HELP) {
        navigateTo(NavigationTarget.HELP);
      } else {
        navigateTo(NavigationTarget.LOGIN);
      }
    } else if (onClick) {
      onClick();
    }
  };

  return (
    <header
      className={`w-full h-[5rem] bg-white py-2 md:py-4 px-6 md:px-[9.375rem] border-b border-[var(--grey-3)] ${className}`}
    >
      <div className="w-full flex items-center justify-between">
        <div className="flex items-center">
          <Image
            src="/assets/givfLogo.png"
            alt="GUNJAN IVF World Logo"
            width={98}
            height={48}
            className="h-auto max-h-[58px] md:h-[48px] w-auto max-w-[118px] md:max-w-[98px]"
            priority
          />
        </div>

        <div className="flex flex-col items-end text-base">
          <span className="text-[var(--dark-grey)] sm:inline">
            {state === HeaderState.HELP
              ? "Having Trouble?"
              : "Have an account?"}
          </span>
          <button
            type="button"
            onClick={handleActionClick}
            className="text-[var(--red-6)] hover:text-[var(--red-7)] cursor-pointer font-medium transition-colors duration-200 rounded px-1"
          >
            {state === HeaderState.HELP ? "Get Help" : "Login"}
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;
