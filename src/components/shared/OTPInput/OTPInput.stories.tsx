import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import OTPInput from "./OTPInput";

const meta: Meta<typeof OTPInput> = {
  title: "Components/Shared/OTPInput",
  component: OTPInput,
  tags: ["autodocs"],
  argTypes: {
    onChange: {
      action: "change",
      description: "Callback when OTP value changes",
    },
    onComplete: {
      action: "complete",
      description: "Callback when OTP is complete",
    },
    length: {
      control: { type: "number", min: 1, max: 8 },
      defaultValue: 6,
      description: "Number of OTP input fields",
    },
    disabled: {
      control: "boolean",
      description: "Disables all input fields",
    },
    error: {
      control: "boolean",
      description: "Shows error state",
    },
    autoFocus: {
      control: "boolean",
      description: "Auto-focuses the first input",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    length: 6,
    disabled: false,
    error: false,
    autoFocus: true,
    onChange: () => {},
  },
  parameters: {
    docs: {
      description: {
        story: "Default OTP input with 6 fields and auto-focus enabled.",
      },
    },
  },
};

export const ErrorState: Story = {
  args: {
    length: 6,
    disabled: false,
    error: true,
    autoFocus: false,
    onChange: () => {},
  },
  parameters: {
    docs: {
      description: {
        story: "OTP input showing error state.",
      },
    },
  },
};

export const Disabled: Story = {
  args: {
    length: 6,
    disabled: true,
    error: false,
    autoFocus: false,
    onChange: () => {},
  },
  parameters: {
    docs: {
      description: {
        story: "OTP input with all fields disabled.",
      },
    },
  },
};

export const WithCallback: Story = {
  args: {
    length: 6,
    disabled: false,
    error: false,
    autoFocus: true,
    onChange: () => {},
    onComplete: () => {},
  },
  parameters: {
    docs: {
      description: {
        story: "OTP input with completion callback.",
      },
    },
  },
};

export const FourDigits: Story = {
  args: {
    length: 4,
    disabled: false,
    error: false,
    autoFocus: true,
    onChange: () => {},
  },
  parameters: {
    docs: {
      description: {
        story: "OTP input with 4 fields.",
      },
    },
  },
};
