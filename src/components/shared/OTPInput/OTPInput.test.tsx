import React from "react";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import OTPInput from "./OTPInput";

describe("OTPInput", () => {
  const mockOnChange = jest.fn();
  const mockOnComplete = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the correct number of input fields", () => {
    render(<OTPInput length={6} onChange={mockOnChange} />);

    const inputs = screen.getAllByRole("textbox");
    expect(inputs).toHaveLength(6);
  });

  it("renders 4 input fields when length is 4", () => {
    render(<OTPInput length={4} onChange={mockOnChange} />);

    const inputs = screen.getAllByRole("textbox");
    expect(inputs).toHaveLength(4);
  });

  it("focuses the first input when autoFocus is true", () => {
    render(<OTPInput length={6} onChange={mockOnChange} autoFocus />);

    const inputs = screen.getAllByRole("textbox");
    expect(inputs[0]).toHaveFocus();
  });

  it("does not focus any input when autoFocus is false", () => {
    render(<OTPInput length={6} onChange={mockOnChange} autoFocus={false} />);

    const inputs = screen.getAllByRole("textbox");
    inputs.forEach((input) => {
      expect(input).not.toHaveFocus();
    });
  });

  it("accepts numeric input and calls onChange", async () => {
    const user = userEvent.setup();
    render(<OTPInput length={6} onChange={mockOnChange} />);

    const inputs = screen.getAllByRole("textbox");
    await user.type(inputs[0], "5");

    expect(inputs[0]).toHaveValue("5");
    expect(mockOnChange).toHaveBeenCalledWith("5");
  });

  it("rejects non-numeric input", async () => {
    const user = userEvent.setup();
    render(<OTPInput length={6} onChange={mockOnChange} />);

    const inputs = screen.getAllByRole("textbox");
    await user.type(inputs[0], "a");

    expect(inputs[0]).toHaveValue("");
    expect(mockOnChange).not.toHaveBeenCalled();
  });

  it("moves focus to next input after entering a digit", async () => {
    const user = userEvent.setup();
    render(<OTPInput length={6} onChange={mockOnChange} />);

    const inputs = screen.getAllByRole("textbox");
    await user.type(inputs[0], "1");

    expect(inputs[1]).toHaveFocus();
  });

  it("does not move focus from the last input", async () => {
    const user = userEvent.setup();
    render(<OTPInput length={3} onChange={mockOnChange} />);

    const inputs = screen.getAllByRole("textbox");
    inputs[2].focus();
    await user.type(inputs[2], "9");

    expect(inputs[2]).toHaveFocus();
  });

  it("moves focus to previous input on backspace when current input is empty", async () => {
    const user = userEvent.setup();
    render(<OTPInput length={6} onChange={mockOnChange} />);

    const inputs = screen.getAllByRole("textbox");
    inputs[1].focus();
    await user.keyboard("{Backspace}");

    expect(inputs[0]).toHaveFocus();
  });

  it("does not move focus on backspace when current input has value", async () => {
    const user = userEvent.setup();
    render(<OTPInput length={6} onChange={mockOnChange} />);

    const inputs = screen.getAllByRole("textbox");
    await user.type(inputs[1], "5");
    await user.keyboard("{Backspace}");

    expect(inputs[1]).toHaveFocus();
    expect(inputs[1]).toHaveValue("5");
  });

  it("calls onChange with complete OTP value", async () => {
    const user = userEvent.setup();
    render(<OTPInput length={3} onChange={mockOnChange} />);

    const inputs = screen.getAllByRole("textbox");
    await user.type(inputs[0], "1");
    await user.type(inputs[1], "2");
    await user.type(inputs[2], "3");

    expect(mockOnChange).toHaveBeenLastCalledWith("123");
  });

  it("calls onComplete when all digits are entered", async () => {
    const user = userEvent.setup();
    render(
      <OTPInput
        length={3}
        onChange={mockOnChange}
        onComplete={mockOnComplete}
      />
    );

    const inputs = screen.getAllByRole("textbox");
    await user.type(inputs[0], "1");
    await user.type(inputs[1], "2");
    await user.type(inputs[2], "3");

    expect(mockOnComplete).toHaveBeenCalledWith("123");
  });

  it("does not call onComplete when not all digits are entered", async () => {
    const user = userEvent.setup();
    render(
      <OTPInput
        length={3}
        onChange={mockOnChange}
        onComplete={mockOnComplete}
      />
    );

    const inputs = screen.getAllByRole("textbox");
    await user.type(inputs[0], "1");
    await user.type(inputs[1], "2");

    expect(mockOnComplete).not.toHaveBeenCalled();
  });

  it("disables all inputs when disabled prop is true", () => {
    render(<OTPInput length={6} onChange={mockOnChange} disabled />);

    const inputs = screen.getAllByRole("textbox");
    inputs.forEach((input) => {
      expect(input).toBeDisabled();
    });
  });

  it("applies error styles when error prop is true", () => {
    render(<OTPInput length={6} onChange={mockOnChange} error />);

    const inputs = screen.getAllByRole("textbox");
    inputs.forEach((input) => {
      expect(input).toHaveClass("border-red-500");
      expect(input).toHaveClass("text-red-500");
    });
  });

  it("applies normal styles when error prop is false", () => {
    render(<OTPInput length={6} onChange={mockOnChange} error={false} />);

    const inputs = screen.getAllByRole("textbox");
    inputs.forEach((input) => {
      expect(input).toHaveClass("border-gray-300");
      expect(input).toHaveClass("text-gray-700");
    });
  });

  it("replaces existing digit when typing over it", async () => {
    const user = userEvent.setup();
    render(<OTPInput length={6} onChange={mockOnChange} />);

    const inputs = screen.getAllByRole("textbox");
    await user.type(inputs[0], "1");
    await user.clear(inputs[0]);
    await user.type(inputs[0], "2");

    expect(inputs[0]).toHaveValue("2");
  });

  it("handles rapid input correctly", async () => {
    const user = userEvent.setup();
    render(
      <OTPInput
        length={4}
        onChange={mockOnChange}
        onComplete={mockOnComplete}
      />
    );

    const inputs = screen.getAllByRole("textbox");

    // Simulate rapid typing
    await user.type(inputs[0], "1234");

    // Only the first character should be in the first input
    expect(inputs[0]).toHaveValue("1");
    expect(mockOnChange).toHaveBeenCalledWith("1");
  });

  it("clears input and calls onChange when empty string is entered", async () => {
    const user = userEvent.setup();
    render(<OTPInput length={6} onChange={mockOnChange} />);

    const inputs = screen.getAllByRole("textbox");
    await user.type(inputs[0], "5");
    await user.clear(inputs[0]);

    expect(inputs[0]).toHaveValue("");
    expect(mockOnChange).toHaveBeenLastCalledWith("");
  });

  it("has correct maxLength attribute on inputs", () => {
    render(<OTPInput length={6} onChange={mockOnChange} />);

    const inputs = screen.getAllByRole("textbox");
    inputs.forEach((input) => {
      expect(input).toHaveAttribute("maxLength", "1");
    });
  });

  it("has correct type attribute on inputs", () => {
    render(<OTPInput length={6} onChange={mockOnChange} />);

    const inputs = screen.getAllByRole("textbox");
    inputs.forEach((input) => {
      expect(input).toHaveAttribute("type", "text");
    });
  });
});
