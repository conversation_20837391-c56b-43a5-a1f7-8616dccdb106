"use client";
import React, { useState, useRef, ChangeEvent, KeyboardEvent } from "react";

interface OTPInputProps {
  length: number;
  onChange: (otp: string) => void;
  onComplete?: (otp: string) => void;
  disabled?: boolean;
  error?: boolean;
  autoFocus?: boolean;
}

const OTPInput: React.FC<OTPInputProps> = ({
  length,
  onChange,
  onComplete,
  disabled = false,
  error = false,
  autoFocus = false,
}) => {
  const [otp, setOtp] = useState<string[]>(Array(length).fill(""));
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const handleChange = (e: ChangeEvent<HTMLInputElement>, index: number) => {
    const value = e.target.value;
    if (/^[0-9]$/.test(value) || value === "") {
      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);
      onChange(newOtp.join(""));

      if (value !== "" && index < length - 1) {
        inputRefs.current[index + 1]?.focus();
      }

      if (newOtp.every((digit) => digit !== "")) {
        onComplete?.(newOtp.join(""));
      }
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>, index: number) => {
    if (e.key === "Backspace" && otp[index] === "" && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const containerClass = `flex justify-center space-x-1 md:space-x-4 ${
    disabled ? "cursor-not-allowed" : ""
  }`;
  const inputClass = `w-10 h-10 md:w-14 md:h-14 text-center text-2xl font-semibold border transition-all duration-200
    ${
      error
        ? "border-red-500 text-red-500 focus:ring-red-500"
        : "border-gray-300 text-gray-700 focus:ring-blue-500"
    }
    focus:border-transparent focus:ring-2 
    ${disabled ? "bg-gray-100" : "bg-white"}`;

  return (
    <div className={containerClass}>
      {otp.map((digit, index) => (
        <input
          key={index}
          ref={(el) => {
            inputRefs.current[index] = el;
          }}
          type="text"
          maxLength={1}
          value={digit}
          onChange={(e) => handleChange(e, index)}
          onKeyDown={(e) => handleKeyDown(e, index)}
          disabled={disabled}
          className={inputClass}
          autoFocus={autoFocus && index === 0}
        />
      ))}
    </div>
  );
};

export default OTPInput;
