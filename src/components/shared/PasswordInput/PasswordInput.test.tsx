import { render, screen, fireEvent } from "@testing-library/react";
import PasswordInput from "./PasswordInput";

describe("PasswordInput", () => {
  it("renders the input with the correct placeholder", () => {
    render(
      <PasswordInput
        id="password"
        value=""
        onChange={() => {}}
        placeholder="Enter your password"
      />
    );
    expect(
      screen.getByPlaceholderText("Enter your password")
    ).toBeInTheDocument();
  });

  it("toggles password visibility when the eye icon is clicked", () => {
    render(
      <PasswordInput
        id="password"
        value="secret"
        onChange={() => {}}
        placeholder="Enter your password"
      />
    );

    const input = screen.getByPlaceholderText("Enter your password");
    const button = screen.getByRole("button");

    // Initially, the password should be masked
    expect(input).toHaveAttribute("type", "password");

    // Click the button to show the password
    fireEvent.click(button);
    expect(input).toHaveAttribute("type", "text");

    // Click the button again to hide the password
    fireEvent.click(button);
    expect(input).toHaveAttribute("type", "password");
  });

  it("calls the onChange handler when the input value changes", () => {
    const handleChange = jest.fn();
    render(
      <PasswordInput
        id="password"
        value=""
        onChange={handleChange}
        placeholder="Enter your password"
      />
    );

    const input = screen.getByPlaceholderText("Enter your password");
    fireEvent.change(input, { target: { value: "new-password" } });
    expect(handleChange).toHaveBeenCalledTimes(1);
  });
});
