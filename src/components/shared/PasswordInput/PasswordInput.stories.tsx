import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import PasswordInput from "./PasswordInput";

const meta: Meta<typeof PasswordInput> = {
  title: "Components/PasswordInput",
  component: PasswordInput,
  tags: ["autodocs"],
  argTypes: {
    id: {
      control: "text",
      description: "The ID of the input element",
    },
    value: {
      control: "text",
      description: "The value of the input element",
    },
    onChange: {
      action: "changed",
      description: "Callback function when the input value changes",
    },
    placeholder: {
      control: "text",
      description: "The placeholder text for the input",
    },
    disabled: {
      control: "boolean",
      description: "Whether the input is disabled",
    },
    minLength: {
      control: "number",
      description: "The minimum length of the password",
    },
  },
};

export default meta;
type Story = StoryObj<typeof PasswordInput>;

export const Default: Story = {
  args: {
    id: "password",
    value: "password123",
    placeholder: "Enter your password",
  },
};

export const Disabled: Story = {
  args: {
    id: "password",
    value: "password123",
    placeholder: "Enter your password",
    disabled: true,
  },
};
