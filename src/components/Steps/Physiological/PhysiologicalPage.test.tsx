import React from "react";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import PhysiologicalPage from "./PhysiologicalPage";

// Mock Next.js Image component
jest.mock("next/image", () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return function MockImage({ src, alt, ...props }: any) {
    // eslint-disable-next-line @next/next/no-img-element
    return <img src={src} alt={alt} {...props} />;
  };
});

describe("PhysiologicalPage", () => {
  const mockOnNext = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Component Rendering", () => {
    it("renders without crashing", () => {
      render(<PhysiologicalPage />);
      expect(screen.getByText("Biological Factors")).toBeInTheDocument();
    });

    it("renders step indicator correctly", () => {
      render(<PhysiologicalPage />);
      expect(
        screen.getByText((content, element) => {
          const text = element?.textContent || "";
          const hasStepInfo =
            text.includes("Step") &&
            text.includes("1") &&
            text.includes("of") &&
            text.includes("5");
          const isParagraph = element?.tagName === "P";
          return hasStepInfo && isParagraph;
        })
      ).toBeInTheDocument();
      expect(screen.getByText("Biological Factors")).toBeInTheDocument();
    });

    it("renders header with login state", () => {
      render(<PhysiologicalPage />);
      expect(screen.getByText("Have an account?")).toBeInTheDocument();
      expect(screen.getByText("Login")).toBeInTheDocument();
    });

    it("renders footer component", () => {
      render(<PhysiologicalPage />);
      expect(screen.getByText("© 2025 Gunjan IVF")).toBeInTheDocument();
      expect(screen.getByText("Privacy Policy")).toBeInTheDocument();
      expect(screen.getByText("Terms & Conditions")).toBeInTheDocument();
    });

    it("renders need help section", () => {
      render(<PhysiologicalPage />);
      expect(screen.getByText("Need Help?")).toBeInTheDocument();
      expect(screen.getByText("+91-**********")).toBeInTheDocument();
    });
  });

  describe("Form Fields", () => {
    it("renders all form fields with default values", () => {
      render(<PhysiologicalPage />);

      // Check age field
      expect(screen.getByText("Age")).toBeInTheDocument();
      const ageInput = screen.getByDisplayValue("28");
      expect(ageInput).toHaveValue(28);
      expect(screen.getByText("Yrs")).toBeInTheDocument();

      // Check height field
      expect(screen.getByText("Height")).toBeInTheDocument();
      const heightInput = screen.getByDisplayValue("167.43");
      expect(heightInput).toHaveValue(167.43);
      expect(screen.getByText("cm")).toBeInTheDocument();

      // Check weight field
      expect(screen.getByText("Weight")).toBeInTheDocument();
      const weightInput = screen.getByDisplayValue("52");
      expect(weightInput).toHaveValue(52);
      expect(screen.getByText("kg")).toBeInTheDocument();

      // Check menstrual regularity
      expect(screen.getByText("Menstrual Regularity")).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: "Regular" }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: "Irregular" }),
      ).toBeInTheDocument();

      // Check infertility duration
      expect(screen.getByText("Infertility Duration")).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: "<6 months" }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: "6—12 months" }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: ">12 months" }),
      ).toBeInTheDocument();

      // Check IVF attempts
      expect(screen.getByText("Number of IVF attempts")).toBeInTheDocument();
      const ivfInput = screen.getByDisplayValue("0");
      expect(ivfInput).toHaveValue(0);

      // Check known condition
      expect(screen.getByText("Known Condition")).toBeInTheDocument();
      expect(
        screen.getByDisplayValue("PCOS, Thyroid, High FSH"),
      ).toBeInTheDocument();
    });

    it("renders Next Step button", () => {
      render(<PhysiologicalPage />);
      expect(
        screen.getByRole("button", { name: /next step/i }),
      ).toBeInTheDocument();
    });
  });

  describe("Form Interactions", () => {
    it("updates age field when user types", async () => {
      const user = userEvent.setup();
      render(<PhysiologicalPage />);

      const ageInput = screen.getByDisplayValue("28");
      await user.clear(ageInput);
      await user.type(ageInput, "30");

      expect(ageInput).toHaveValue(30);
    });

    it("updates height field when user types", async () => {
      const user = userEvent.setup();
      render(<PhysiologicalPage />);

      const heightInput = screen.getByDisplayValue("167.43");
      await user.clear(heightInput);
      await user.type(heightInput, "170.5");

      expect(heightInput).toHaveValue(170.5);
    });

    it("updates weight field when user types", async () => {
      const user = userEvent.setup();
      render(<PhysiologicalPage />);

      const weightInput = screen.getByDisplayValue("52");
      await user.clear(weightInput);
      await user.type(weightInput, "60");

      expect(weightInput).toHaveValue(60);
    });

    it("toggles menstrual regularity selection", async () => {
      const user = userEvent.setup();
      render(<PhysiologicalPage />);

      const regularButton = screen.getByRole("button", { name: "Regular" });
      const irregularButton = screen.getByRole("button", { name: "Irregular" });

      // Regular should be selected by default (has violet background)
      expect(regularButton).toHaveClass("bg-[var(--violet-6)]");
      expect(irregularButton).toHaveClass("border-[var(--grey-3)]");

      // Click irregular
      await user.click(irregularButton);

      expect(irregularButton).toHaveClass("bg-[var(--violet-6)]");
      expect(regularButton).toHaveClass("border-[var(--grey-3)]");
    });

    it("toggles infertility duration selection", async () => {
      const user = userEvent.setup();
      render(<PhysiologicalPage />);

      const option1 = screen.getByRole("button", { name: "<6 months" });
      const option2 = screen.getByRole("button", { name: "6—12 months" });
      const option3 = screen.getByRole("button", { name: ">12 months" });

      // <6 months should be selected by default
      expect(option1).toHaveClass("bg-[var(--violet-6)]");
      expect(option2).toHaveClass("border-[var(--grey-3)]");
      expect(option3).toHaveClass("border-[var(--grey-3)]");

      // Click option 2
      await user.click(option2);

      expect(option2).toHaveClass("bg-[var(--violet-6)]");
      expect(option1).toHaveClass("border-[var(--grey-3)]");
      expect(option3).toHaveClass("border-[var(--grey-3)]");
    });

    it("updates IVF attempts field when user types", async () => {
      const user = userEvent.setup();
      render(<PhysiologicalPage />);

      const ivfInput = screen.getByDisplayValue("0");
      await user.clear(ivfInput);
      await user.type(ivfInput, "3");

      expect(ivfInput).toHaveValue(3);
    });

    it("updates known condition when user selects different option", async () => {
      const user = userEvent.setup();
      render(<PhysiologicalPage />);

      const conditionSelect = screen.getByDisplayValue(
        "PCOS, Thyroid, High FSH",
      );
      await user.selectOptions(conditionSelect, "PCOS");

      expect(conditionSelect).toHaveValue("PCOS");
    });
  });

  describe("Next Step Functionality", () => {
    it("calls onNext with form data when Next Step button is clicked", async () => {
      const user = userEvent.setup();
      render(<PhysiologicalPage onNext={mockOnNext} />);

      const nextButton = screen.getByRole("button", { name: /next step/i });
      await user.click(nextButton);

      expect(mockOnNext).toHaveBeenCalledWith({
        age: "28",
        height: "167.43",
        weight: "52",
        menstrualRegularity: "regular",
        infertilityDuration: "<6 months",
        ivfAttempts: "0",
        knownCondition: "PCOS, Thyroid, High FSH",
      });
    });

    it("calls onNext with updated form data", async () => {
      const user = userEvent.setup();
      render(<PhysiologicalPage onNext={mockOnNext} />);

      // Update some form fields
      const ageInput = screen.getByDisplayValue("28");
      await user.clear(ageInput);
      await user.type(ageInput, "32");

      const irregularButton = screen.getByRole("button", { name: "Irregular" });
      await user.click(irregularButton);

      const conditionSelect = screen.getByDisplayValue(
        "PCOS, Thyroid, High FSH",
      );
      await user.selectOptions(conditionSelect, "Endometriosis");

      const nextButton = screen.getByRole("button", { name: /next step/i });
      await user.click(nextButton);

      expect(mockOnNext).toHaveBeenCalledWith({
        age: "32",
        height: "167.43",
        weight: "52",
        menstrualRegularity: "irregular",
        infertilityDuration: "<6 months",
        ivfAttempts: "0",
        knownCondition: "Endometriosis",
      });
    });

    it("does not throw error when onNext is not provided", async () => {
      const user = userEvent.setup();
      render(<PhysiologicalPage />);

      const nextButton = screen.getByRole("button", { name: /next step/i });

      expect(() => user.click(nextButton)).not.toThrow();
    });
  });

  describe("Accessibility", () => {
    it("has proper form labels", () => {
      render(<PhysiologicalPage />);

      expect(screen.getByText("Age")).toBeInTheDocument();
      expect(screen.getByText("Height")).toBeInTheDocument();
      expect(screen.getByText("Weight")).toBeInTheDocument();
      expect(screen.getByText("Number of IVF attempts")).toBeInTheDocument();
      expect(screen.getByText("Known Condition")).toBeInTheDocument();
    });

    it("has proper button roles", () => {
      render(<PhysiologicalPage />);

      expect(
        screen.getByRole("button", { name: "Regular" }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: "Irregular" }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: "<6 months" }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: "6—12 months" }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: ">12 months" }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: /next step/i }),
      ).toBeInTheDocument();
    });
  });

  describe("Custom Props", () => {
    it("applies custom className", () => {
      const { container } = render(
        <PhysiologicalPage className="custom-class" />,
      );

      expect(container.firstChild).toHaveClass("custom-class");
    });
  });
});
