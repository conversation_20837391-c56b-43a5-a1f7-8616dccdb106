import React, { useState } from "react";
import Header, { HeaderState } from "../../shared/Header/Header";
import Footer from "../../shared/Footer/Footer";
import Button, { ButtonType } from "../../shared/Button/Button";
import NeedHelp from "../../shared/NeedHelp/NeedHelp";
import Image from "next/image";

export interface PhysiologicalPageProps {
  onNext?: (data: BiologicalFactorsData) => void;
  className?: string;
}

export interface BiologicalFactorsData {
  age: string;
  height: string;
  weight: string;
  menstrualRegularity: "regular" | "irregular";
  infertilityDuration: "<6 months" | "6-12 months" | ">12 months";
  ivfAttempts: string;
  knownCondition: string;
}

const PhysiologicalPage: React.FC<PhysiologicalPageProps> = ({
  onNext,
  className = "",
}) => {
  const [formData, setFormData] = useState<BiologicalFactorsData>({
    age: "28",
    height: "167.43",
    weight: "52",
    menstrualRegularity: "regular",
    infertilityDuration: "<6 months",
    ivfAttempts: "0",
    knownCondition: "PCOS, Thyroid, High FSH",
  });

  const handleInputChange = (
    field: keyof BiologicalFactorsData,
    value: string,
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleNextStep = () => {
    if (onNext) {
      onNext(formData);
    }
  };

  return (
    <div className={`min-h-screen flex flex-col ${className}`}>
      <Header state={HeaderState.LOGIN} />

      <main className="flex-1 flex justify-center py-4 px-6 md:py-6 md:px-[9.375rem]">
        <div className="max-w-[39.625rem] w-full">
          <div className="text-center flex flex-col gap-4 mb-10">
            <p className="text-[var(--violet-6)] text-sm font-medium">
              Step 1 of 5
            </p>
            <h1 className="text-[var(--grey-7)] text-center text-[1.75rem] font-bold">
              Biological Factors
              <div className="flex justify-center py-1">
                <Image
                  src="/assets/loginPage/Line.png"
                  alt="Decorative line"
                  className="h-1 w-16"
                  width={100}
                  height={9}
                />
              </div>
            </h1>
          </div>

          <div className="bg-white rounded-lg">
            <form className="space-y-6">
              {/* Age */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Age
                </label>
                <div className="relative">
                  <input
                    type="number"
                    value={formData.age}
                    onChange={(e) => handleInputChange("age", e.target.value)}
                    className="w-full h-[3.125rem] px-4 py-3 pr-12 border border-[var(--grey-3)] rounded-sm text-[var(--grey-7)] text-base font-bold bg-white focus:outline-none focus:ring-2 focus:ring-[var(--violet-6)] focus:border-[var(--violet-6)]"
                  />
                  <span className="absolute right-4 top-1/2 transform -translate-y-1/2 text-[var(--grey-5)] text-base pointer-events-none">
                    Yrs
                  </span>
                </div>
              </div>

              {/* Height */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Height
                </label>
                <div className="relative">
                  <input
                    type="number"
                    step="0.01"
                    value={formData.height}
                    onChange={(e) =>
                      handleInputChange("height", e.target.value)
                    }
                    className="w-full h-[3.125rem] px-4 py-3 pr-12 border border-[var(--grey-3)] rounded-sm text-[var(--grey-7)] text-base font-bold bg-white focus:outline-none focus:ring-2 focus:ring-[var(--violet-6)] focus:border-[var(--violet-6)]"
                  />
                  <span className="absolute right-4 top-1/2 transform -translate-y-1/2 text-[var(--grey-5)] text-base pointer-events-none">
                    cm
                  </span>
                </div>
              </div>

              {/* Weight */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Weight
                </label>
                <div className="relative">
                  <input
                    type="number"
                    value={formData.weight}
                    onChange={(e) =>
                      handleInputChange("weight", e.target.value)
                    }
                    className="w-full h-[3.125rem] px-4 py-3 pr-12 border border-[var(--grey-3)] rounded-sm text-[var(--grey-7)] text-base font-bold bg-white focus:outline-none focus:ring-2 focus:ring-[var(--violet-6)] focus:border-[var(--violet-6)]"
                  />
                  <span className="absolute right-4 top-1/2 transform -translate-y-1/2 text-[var(--grey-5)] text-base pointer-events-none">
                    kg
                  </span>
                </div>
              </div>

              {/* Menstrual Regularity */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Menstrual Regularity
                </label>
                <div className="flex gap-4">
                  <button
                    type="button"
                    onClick={() =>
                      handleInputChange("menstrualRegularity", "regular")
                    }
                    className={`py-3 w-[8.438rem] rounded-[0.25rem] text-center text-base font-bold transition-all duration-200 shadow-sm ${
                      formData.menstrualRegularity === "regular"
                        ? "bg-[var(--violet-6)] border border-[var(--violet-8)] text-white font-bold"
                        : "text-[var(--grey-6)] border border-[var(--grey-3)] hover:bg-[var(--grey-3)] font-medium"
                    }`}
                  >
                    Regular
                  </button>
                  <button
                    type="button"
                    onClick={() =>
                      handleInputChange("menstrualRegularity", "irregular")
                    }
                    className={`px-6 py-3 w-[8.438rem] rounded-[0.25rem] text-base transition-all duration-200 shadow-sm ${
                      formData.menstrualRegularity === "irregular"
                        ? "bg-[var(--violet-6)] border border-[var(--violet-8)] text-white font-bold"
                        : "border border-[var(--grey-3)] text-[var(--grey-6)] hover:bg-[var(--grey-3)] font-medium"
                    }`}
                  >
                    Irregular
                  </button>
                </div>
              </div>

              {/* Infertility Duration */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Infertility Duration
                </label>
                <div className="flex gap-4 flex-wrap">
                  <button
                    type="button"
                    onClick={() =>
                      handleInputChange("infertilityDuration", "<6 months")
                    }
                    className={`py-3 w-[8.438rem] rounded-[0.25rem] text-center text-base font-bold transition-all duration-200 shadow-sm ${
                      formData.infertilityDuration === "<6 months"
                        ? "bg-[var(--violet-6)] border border-[var(--violet-8)] text-white font-bold"
                        : "border border-[var(--grey-3)] text-[var(--grey-6)] hover:bg-[var(--grey-3)] font-medium"
                    }`}
                  >
                    &lt;6 months
                  </button>
                  <button
                    type="button"
                    onClick={() =>
                      handleInputChange("infertilityDuration", "6-12 months")
                    }
                    className={`py-3 w-[8.438rem] rounded-[0.25rem] text-center text-base font-bold transition-all duration-200 shadow-sm ${
                      formData.infertilityDuration === "6-12 months"
                        ? "bg-[var(--violet-6)] border border-[var(--violet-8)] text-white font-bold"
                        : "border border-[var(--grey-3)] text-[var(--grey-6)] hover:bg-[var(--grey-3)] font-medium"
                    }`}
                  >
                    6—12 months
                  </button>
                  <button
                    type="button"
                    onClick={() =>
                      handleInputChange("infertilityDuration", ">12 months")
                    }
                    className={`py-3 w-[8.438rem] rounded-[0.25rem] text-center text-base transition-all duration-200 shadow-sm ${
                      formData.infertilityDuration === ">12 months"
                        ? "bg-[var(--violet-6)] border border-[var(--violet-8)] text-white font-bold"
                        : "border border-[var(--grey-3)] text-[var(--grey-6)] hover:bg-[var(--grey-3)] font-medium"
                    }`}
                  >
                    &gt;12 months
                  </button>
                </div>
              </div>

              {/* Number of IVF Attempts */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Number of IVF Attempts
                </label>
                <input
                  type="number"
                  min="0"
                  value={formData.ivfAttempts}
                  onChange={(e) =>
                    handleInputChange("ivfAttempts", e.target.value)
                  }
                  className="w-full h-[3.125rem] px-4 py-3 border border-[var(--grey-3)] rounded-sm text-[var(--grey-7)] text-base font-bold bg-white focus:outline-none focus:ring-2 focus:ring-[var(--violet-6)] focus:border-[var(--violet-6)]"
                />
              </div>

              {/* Known Condition */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Known Condition
                </label>
                <select
                  value={formData.knownCondition}
                  onChange={(e) =>
                    handleInputChange("knownCondition", e.target.value)
                  }
                  className="w-full h-[3.125rem] px-4 py-3 border border-[var(--grey-3)] rounded-[0.25rem] text-[var(--grey-7)] text-base font-bold bg-white focus:outline-none focus:ring-2 focus:ring-[var(--violet-6)] focus:border-[var(--violet-6)] appearance-none"
                  style={{
                    backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                    backgroundRepeat: "no-repeat",
                    backgroundPosition: "right 1rem center",
                    backgroundSize: "1rem",
                  }}
                >
                  <option value="PCOS, Thyroid, High FSH">
                    PCOS, Thyroid, High FSH
                  </option>
                  <option value="PCOS">PCOS</option>
                  <option value="Thyroid">Thyroid</option>
                  <option value="High FSH">High FSH</option>
                  <option value="Endometriosis">Endometriosis</option>
                  <option value="Other">Other</option>
                  <option value="None">None</option>
                </select>
              </div>

              {/* Next Step Button */}
              <div className="pt-4">
                <Button
                  type={ButtonType.PRIMARY}
                  text="Next Step"
                  onClick={handleNextStep}
                  className="h-[3.125rem]"
                  icon={
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="9,18 15,12 9,6"></polyline>
                    </svg>
                  }
                />
              </div>
            </form>
          </div>
        </div>
      </main>

      {/* Need Help Section */}
      <div className="bg-white py-6 px-6 md:px-[9.375rem]">
        <div className="max-w-2xl mx-auto flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <NeedHelp
            phoneNumber="+91-9990044555"
            className="order-2 md:order-1"
          />
        </div>
      </div>

      <Footer className="mt-auto" />
    </div>
  );
};

export default PhysiologicalPage;
