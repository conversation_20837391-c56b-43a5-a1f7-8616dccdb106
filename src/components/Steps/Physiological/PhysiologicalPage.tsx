import React, { useState } from "react";
import Image from "next/image";
import Header, { HeaderState } from "../../shared/Header/Header";
import Footer from "../../shared/Footer/Footer";
import Button, { ButtonType } from "../../shared/Button/Button";
import NeedHelp from "../../shared/NeedHelp/NeedHelp";
import ToggleButton from "../../shared/ToggleButton/ToggleButton";
import Input from "../../shared/Input/Input";

export interface PhysiologicalPageProps {
  onNext?: (data: BiologicalFactorsData) => void;
  className?: string;
}

export interface BiologicalFactorsData {
  age: string;
  height: string;
  weight: string;
  menstrualRegularity: "regular" | "irregular";
  infertilityDuration: "<6 months" | "6-12 months" | ">12 months";
  ivfAttempts: string;
  knownCondition: string;
}

const PhysiologicalPage: React.FC<PhysiologicalPageProps> = ({
  onNext,
  className = "",
}) => {
  const [formData, setFormData] = useState<BiologicalFactorsData>({
    age: "28",
    height: "167.43",
    weight: "52",
    menstrualRegularity: "regular",
    infertilityDuration: "<6 months",
    ivfAttempts: "0",
    knownCondition: "PCOS, Thyroid, High FSH",
  });

  const handleInputChange = (
    field: keyof BiologicalFactorsData,
    value: string
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleNextStep = () => {
    if (onNext) {
      onNext(formData);
    }
  };

  return (
    <div className={`min-h-screen flex flex-col ${className}`}>
      <Header state={HeaderState.LOGIN} />

      <main className="flex-1 flex justify-center py-4 px-6 md:py-6 md:px-[9.375rem]">
        <div className="max-w-[39.625rem] w-full">
          <div className="text-center flex flex-col gap-4 mb-10">
            <p className="text-[var(--violet-6)] text-base font-medium">
              Step <span className="text-[var(--grey-6)]">{1}</span> of{" "}
              <span className="text-[var(--grey-6)]">{5}</span>
            </p>
            <h1 className="text-[var(--grey-7)] text-center text-[1.75rem] font-bold">
              Biological Factors
              <div className="flex justify-center py-1">
                <Image
                  src="/assets/loginPage/Line.png"
                  alt="Decorative line"
                  className="h-1 w-16"
                  width={100}
                  height={9}
                />
              </div>
            </h1>
          </div>

          <div className="bg-white rounded-lg">
            <form className="space-y-6">
              {/* Age */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Age
                </label>
                <div className="relative">
                  <Input
                    type="number"
                    value={formData.age}
                    onChange={(value) =>
                      setFormData((prev) => ({
                        ...prev,
                        age: value,
                      }))
                    }
                    className="pr-12"
                  />
                  <span className="absolute right-4 top-1/2 transform -translate-y-1/2 text-[var(--grey-6)] text-base font-medium pointer-events-none">
                    Yrs
                  </span>
                </div>
              </div>

              {/* Height */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Height
                </label>
                <div className="relative">
                  <Input
                    type="number"
                    value={formData.height}
                    onChange={(value) =>
                      setFormData((prev) => ({
                        ...prev,
                        height: value,
                      }))
                    }
                    className="pr-12"
                  />
                  <span className="absolute right-4 top-1/2 transform -translate-y-1/2 text-[var(--grey-6)] text-base font-medium pointer-events-none">
                    cm
                  </span>
                </div>
              </div>

              {/* Weight */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Weight
                </label>
                <div className="relative">
                  <Input
                    type="number"
                    value={formData.weight}
                    onChange={(value) =>
                      setFormData((prev) => ({
                        ...prev,
                        weight: value,
                      }))
                    }
                    className="pr-12"
                  />
                  <span className="absolute right-4 top-1/2 transform -translate-y-1/2 text-[var(--grey-6)] text-base font-medium pointer-events-none">
                    kg
                  </span>
                </div>
              </div>

              {/* Menstrual Regularity */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Menstrual Regularity
                </label>
                <div className="flex gap-4">
                  <ToggleButton
                    variant="compact"
                    isSelected={formData.menstrualRegularity === "regular"}
                    onClick={() =>
                      handleInputChange("menstrualRegularity", "regular")
                    }
                  >
                    Regular
                  </ToggleButton>
                  <ToggleButton
                    variant="compact"
                    isSelected={formData.menstrualRegularity === "irregular"}
                    onClick={() =>
                      handleInputChange("menstrualRegularity", "irregular")
                    }
                  >
                    Irregular
                  </ToggleButton>
                </div>
              </div>

              {/* Infertility Duration */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Infertility Duration
                </label>
                <div className="flex gap-4 flex-wrap">
                  <ToggleButton
                    isSelected={formData.infertilityDuration === "<6 months"}
                    onClick={() =>
                      handleInputChange("infertilityDuration", "<6 months")
                    }
                  >
                    &lt;6 months
                  </ToggleButton>
                  <ToggleButton
                    isSelected={formData.infertilityDuration === "6-12 months"}
                    onClick={() =>
                      handleInputChange("infertilityDuration", "6-12 months")
                    }
                  >
                    6—12 months
                  </ToggleButton>
                  <ToggleButton
                    isSelected={formData.infertilityDuration === ">12 months"}
                    onClick={() =>
                      handleInputChange("infertilityDuration", ">12 months")
                    }
                  >
                    &gt;12 months
                  </ToggleButton>
                </div>
              </div>

              {/* IVF Attempts */}
              <Input
                type="number"
                label="Number of IVF attempts"
                value={formData.ivfAttempts}
                onChange={(value) =>
                  setFormData((prev) => ({
                    ...prev,
                    ivfAttempts: value,
                  }))
                }
                placeholder="Enter number of attempts"
              />

              {/* Known Condition */}
              <div>
                <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                  Known Condition
                </label>
                <select
                  value={formData.knownCondition}
                  onChange={(e) =>
                    handleInputChange("knownCondition", e.target.value)
                  }
                  className="w-full h-[3.125rem] px-4 py-3 border border-[var(--grey-3)] rounded-[0.25rem] text-[var(--grey-7)] text-base font-bold bg-white focus:outline-none focus:ring-2 focus:ring-[var(--violet-6)] focus:border-[var(--violet-6)] appearance-none"
                  style={{
                    backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                    backgroundRepeat: "no-repeat",
                    backgroundPosition: "right 1rem center",
                    backgroundSize: "1rem",
                  }}
                >
                  <option value="PCOS, Thyroid, High FSH">
                    PCOS, Thyroid, High FSH
                  </option>
                  <option value="PCOS">PCOS</option>
                  <option value="Thyroid">Thyroid</option>
                  <option value="High FSH">High FSH</option>
                  <option value="Endometriosis">Endometriosis</option>
                  <option value="Other">Other</option>
                  <option value="None">None</option>
                </select>
              </div>

              {/* Next Step Button */}
              <div className="pt-4">
                <Button
                  type={ButtonType.PRIMARY}
                  text="Next Step"
                  onClick={handleNextStep}
                  className="h-[3.125rem]"
                  icon={
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="9,18 15,12 9,6"></polyline>
                    </svg>
                  }
                />
              </div>
            </form>
          </div>
        </div>
      </main>

      {/* Need Help Section */}
      <div className="bg-white py-6 px-6 md:px-[9.375rem]">
        <div className="max-w-2xl mx-auto flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <NeedHelp
            phoneNumber="+91-9990044555"
            className="order-2 md:order-1"
          />
        </div>
      </div>

      <Footer className="mt-auto" />
    </div>
  );
};

export default PhysiologicalPage;
