import React from "react";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import RegisterPage from "./RegisterPage";

// Mock Next.js Image component
jest.mock("next/image", () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return function MockImage({ src, alt, ...props }: any) {
    // eslint-disable-next-line @next/next/no-img-element
    return <img src={src} alt={alt} {...props} />;
  };
});

describe("RegisterPage", () => {
  const mockOnRegister = jest.fn();
  const mockOnLoginClick = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Component Rendering", () => {
    it("renders without crashing", () => {
      render(<RegisterPage />);
      expect(screen.getByText("Create Your GIVF Account")).toBeInTheDocument();
    });

    it("renders header with help state", () => {
      render(<RegisterPage />);
      expect(screen.getByText("Having Trouble?")).toBeInTheDocument();
      expect(screen.getByText("Get Help")).toBeInTheDocument();
    });

    it("renders footer component", () => {
      render(<RegisterPage />);
      expect(screen.getByText("© 2025 Gunjan IVF")).toBeInTheDocument();
      // Check footer links specifically
      const footerSection = screen.getByRole("contentinfo");
      expect(
        footerSection.querySelector('a[href="/privacy-policy"]')
      ).toBeInTheDocument();
      expect(
        footerSection.querySelector('a[href="/terms-conditions"]')
      ).toBeInTheDocument();
    });

    it("renders page title and description", () => {
      render(<RegisterPage />);
      expect(screen.getByText("Create Your GIVF Account")).toBeInTheDocument();
      expect(
        screen.getByText(
          "Let's begin your journey with GIVF. Create your secure account to access your IVF tools and progress."
        )
      ).toBeInTheDocument();
    });
  });

  describe("Form Fields", () => {
    it("renders all form fields", () => {
      render(<RegisterPage />);

      // Text inputs by placeholder
      expect(
        screen.getByPlaceholderText("Enter your full name")
      ).toBeInTheDocument();
      expect(
        screen.getByPlaceholderText("Enter your email address")
      ).toBeInTheDocument();
      expect(
        screen.getByPlaceholderText("Enter your phone number")
      ).toBeInTheDocument();
      expect(screen.getByPlaceholderText("Enter your age")).toBeInTheDocument();

      // Labels
      expect(screen.getByText("Full Name")).toBeInTheDocument();
      expect(screen.getByText("Email")).toBeInTheDocument();
      expect(screen.getByText("Sex")).toBeInTheDocument();
      expect(screen.getByText("Date of Birth")).toBeInTheDocument();

      // Toggle buttons
      expect(
        screen.getByRole("button", { name: "Female" })
      ).toBeInTheDocument();
      expect(screen.getByRole("button", { name: "Male" })).toBeInTheDocument();

      // Checkbox
      expect(
        screen.getByLabelText(/I accept the Terms & Privacy Policy/)
      ).toBeInTheDocument();

      // Submit button
      expect(
        screen.getByRole("button", { name: "Continue to Create Password" })
      ).toBeInTheDocument();
    });

    it("has proper placeholders", () => {
      render(<RegisterPage />);

      expect(
        screen.getByPlaceholderText("Enter your full name")
      ).toBeInTheDocument();
      expect(
        screen.getByPlaceholderText("Enter your email address")
      ).toBeInTheDocument();
      expect(
        screen.getByPlaceholderText("Enter your phone number")
      ).toBeInTheDocument();
      expect(screen.getByPlaceholderText("Enter your age")).toBeInTheDocument();
    });

    it("submit button is disabled by default", () => {
      render(<RegisterPage />);
      const submitButton = screen.getByRole("button", {
        name: "Continue to Create Password",
      });
      expect(submitButton).toBeDisabled();
    });
  });

  describe("Form Interactions", () => {
    it("updates full name field when user types", async () => {
      const user = userEvent.setup();
      render(<RegisterPage />);

      const nameInput = screen.getByPlaceholderText("Enter your full name");
      await user.type(nameInput, "John Doe");

      expect(nameInput).toHaveValue("John Doe");
    });

    it("updates email field when user types", async () => {
      const user = userEvent.setup();
      render(<RegisterPage />);

      const emailInput = screen.getByPlaceholderText(
        "Enter your email address"
      );
      await user.type(emailInput, "<EMAIL>");

      expect(emailInput).toHaveValue("<EMAIL>");
    });

    it("updates phone number field when user types", async () => {
      const user = userEvent.setup();
      render(<RegisterPage />);

      const phoneInput = screen.getByPlaceholderText("Enter your phone number");
      await user.type(phoneInput, "1234567890");

      expect(phoneInput).toHaveValue("1234567890");
    });

    it("updates age field when user types", async () => {
      const user = userEvent.setup();
      render(<RegisterPage />);

      const ageInput = screen.getByPlaceholderText("Enter your age");
      await user.type(ageInput, "28");

      expect(ageInput).toHaveValue(28);
    });

    it("toggles sex selection", async () => {
      const user = userEvent.setup();
      render(<RegisterPage />);

      const femaleButton = screen.getByRole("button", { name: "Female" });
      const maleButton = screen.getByRole("button", { name: "Male" });

      // Female should be selected by default
      expect(femaleButton).toHaveClass("bg-[var(--violet-6)]");
      expect(maleButton).toHaveClass("border-[var(--grey-3)]");

      // Click male
      await user.click(maleButton);

      expect(maleButton).toHaveClass("bg-[var(--violet-6)]");
      expect(femaleButton).toHaveClass("border-[var(--grey-3)]");
    });

    it("updates date of birth field", async () => {
      const user = userEvent.setup();
      const { container } = render(<RegisterPage />);

      const dateInput = container.querySelector('input[type="date"]');
      await user.type(dateInput!, "1995-06-15");

      expect(dateInput).toHaveValue("1995-06-15");
    });

    it("toggles terms acceptance checkbox", async () => {
      const user = userEvent.setup();
      render(<RegisterPage />);

      const checkbox = screen.getByLabelText(
        /I accept the Terms & Privacy Policy/
      );
      const submitButton = screen.getByRole("button", {
        name: "Continue to Create Password",
      });

      expect(checkbox).not.toBeChecked();
      expect(submitButton).toBeDisabled();

      await user.click(checkbox);

      expect(checkbox).toBeChecked();
      expect(submitButton).toBeEnabled();
    });
  });

  describe("Form Submission", () => {
    it("calls onRegister with form data when form is submitted", async () => {
      const user = userEvent.setup();
      const { container } = render(
        <RegisterPage onRegister={mockOnRegister} />
      );

      // Fill out form
      await user.type(
        screen.getByPlaceholderText("Enter your full name"),
        "John Doe"
      );
      await user.type(
        screen.getByPlaceholderText("Enter your email address"),
        "<EMAIL>"
      );
      await user.type(
        screen.getByPlaceholderText("Enter your phone number"),
        "1234567890"
      );
      await user.type(screen.getByPlaceholderText("Enter your age"), "28");
      await user.click(screen.getByRole("button", { name: "Male" }));

      const dateInput = container.querySelector('input[type="date"]');
      await user.type(dateInput!, "1995-06-15");

      await user.click(
        screen.getByLabelText(/I accept the Terms & Privacy Policy/)
      );

      const submitButton = screen.getByRole("button", {
        name: "Continue to Create Password",
      });
      await user.click(submitButton);

      expect(mockOnRegister).toHaveBeenCalledWith({
        fullName: "John Doe",
        email: "<EMAIL>",
        phoneNumber: "1234567890",
        age: "28",
        sex: "male",
        dateOfBirth: "1995-06-15",
        acceptsTerms: true,
      });
    });

    it("does not throw error when onRegister is not provided", async () => {
      const user = userEvent.setup();
      render(<RegisterPage />);

      await user.click(
        screen.getByLabelText(/I accept the Terms & Privacy Policy/)
      );
      const submitButton = screen.getByRole("button", {
        name: "Continue to Create Password",
      });

      expect(() => user.click(submitButton)).not.toThrow();
    });
  });

  describe("Login Link", () => {
    it("renders login link section", () => {
      render(<RegisterPage />);
      expect(screen.getByText("Already have an account?")).toBeInTheDocument();
      expect(screen.getByRole("button", { name: "Login" })).toBeInTheDocument();
    });

    it("calls onLoginClick when login link is clicked", async () => {
      const user = userEvent.setup();
      render(<RegisterPage onLoginClick={mockOnLoginClick} />);

      const loginLink = screen.getByRole("button", { name: "Login" });
      await user.click(loginLink);

      expect(mockOnLoginClick).toHaveBeenCalledTimes(1);
    });

    it("does not throw error when onLoginClick is not provided", async () => {
      const user = userEvent.setup();
      render(<RegisterPage />);

      const loginLink = screen.getByRole("button", { name: "Login" });

      expect(() => user.click(loginLink)).not.toThrow();
    });
  });

  describe("Accessibility", () => {
    it("has proper form labels", () => {
      render(<RegisterPage />);

      expect(screen.getByText("Full Name")).toBeInTheDocument();
      expect(screen.getByText("Email")).toBeInTheDocument();
      expect(screen.getByText("Number")).toBeInTheDocument();
      expect(screen.getByText("(Optional)")).toBeInTheDocument();
      expect(screen.getByText("Age")).toBeInTheDocument();
      expect(screen.getByText("Date of Birth")).toBeInTheDocument();
      expect(
        screen.getByLabelText(/I accept the Terms & Privacy Policy/)
      ).toBeInTheDocument();
    });

    it("has proper button roles", () => {
      render(<RegisterPage />);

      expect(
        screen.getByRole("button", { name: "Female" })
      ).toBeInTheDocument();
      expect(screen.getByRole("button", { name: "Male" })).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: "Continue to Create Password" })
      ).toBeInTheDocument();
      expect(screen.getByRole("button", { name: "Login" })).toBeInTheDocument();
    });
  });

  describe("Custom Props", () => {
    it("applies custom className", () => {
      const { container } = render(<RegisterPage className="custom-class" />);

      expect(container.firstChild).toHaveClass("custom-class");
    });
  });
});
