import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import LoginPage from "./LoginPage";

const meta: Meta<typeof LoginPage> = {
  title: "Pages/LoginPage",
  component: LoginPage,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component:
          "The login page component for the IVF application. Features a login form with email/password fields, social login options, and all necessary authentication flows.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    onLogin: {
      action: "login",
      description:
        "Function called when the login button is clicked with email and password",
    },
    onLoginWithOTP: {
      action: "loginWithOTP",
      description:
        "Function called when the 'Login with OTP' button is clicked",
    },
    onContinueWithGoogle: {
      action: "continueWithGoogle",
      description:
        "Fun<PERSON> called when the 'Continue with Google' button is clicked",
    },
    onContinueWithEmail: {
      action: "continueWithEmail",
      description:
        "Function called when the 'Continue with Email' button is clicked",
    },
    onForgotPassword: {
      action: "forgotPassword",
      description:
        "<PERSON><PERSON> called when the 'Forgot Password?' link is clicked",
    },
    onSignUp: {
      action: "signUp",
      description: "<PERSON><PERSON> called when the 'Sign Up' link is clicked",
    },
    onGetHelp: {
      action: "getHelp",
      description:
        "Function called when the 'Get Help' button in header is clicked",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default Login Page
export const Default: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story:
          "The default login page with all interactive elements. Shows the login form, social login options, and help functionality.",
      },
    },
  },
};

// With Interactive Handlers
export const WithHandlers: Story = {
  args: {
    onLogin: (email: string) => alert(`Logging in with ${email}`),
    onLoginWithOTP: () => alert("Redirecting to OTP login..."),
    onContinueWithGoogle: () => alert("Continuing with Google..."),
    onContinueWithEmail: () => alert("Continuing with Email..."),
    onForgotPassword: () => alert("Redirecting to forgot password..."),
    onSignUp: () => alert("Redirecting to sign up..."),
    onGetHelp: () => alert("Opening help section..."),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Login page with custom handlers that demonstrate all interactive functionality. Try clicking different buttons and links to see the alerts.",
      },
    },
  },
};

// Mobile Preview
export const Mobile: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
    docs: {
      description: {
        story:
          "How the login page appears on mobile devices. The form is responsive and optimized for smaller screens.",
      },
    },
  },
};

// Tablet Preview
export const Tablet: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "tablet",
    },
    docs: {
      description: {
        story:
          "How the login page appears on tablet devices. Shows the responsive behavior for medium-sized screens.",
      },
    },
  },
};
