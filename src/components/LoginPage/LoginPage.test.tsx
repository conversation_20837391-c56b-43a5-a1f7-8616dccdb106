import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import LoginPage from "./LoginPage";

describe("LoginPage", () => {
  const mockProps = {
    onLogin: jest.fn(),
    onLoginWithOTP: jest.fn(),
    onContinueWithGoogle: jest.fn(),
    onContinueWithEmail: jest.fn(),
    onForgotPassword: jest.fn(),
    onSignUp: jest.fn(),
    onGetHelp: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders correctly", () => {
    render(<LoginPage {...mockProps} />);

    expect(screen.getByText("Welcome Back")).toBeInTheDocument();
    expect(
      screen.getByText(/Enter your credentials to log in and access your/),
    ).toBeInTheDocument();
    expect(screen.getByLabelText("Email or Number")).toBeInTheDocument();
    expect(screen.getByLabelText("Password")).toBeInTheDocument();
  });

  it("renders login form elements", () => {
    render(<LoginPage {...mockProps} />);

    expect(
      screen.getByRole("textbox", { name: /email or number/i }),
    ).toBeInTheDocument();
    expect(screen.getByLabelText("Password")).toBeInTheDocument();
    expect(
      screen.getByRole("checkbox", { name: /remember me/i }),
    ).toBeInTheDocument();
    expect(screen.getByText("Forgot Password?")).toBeInTheDocument();
  });

  it("renders login buttons", () => {
    render(<LoginPage {...mockProps} />);

    expect(screen.getByRole("button", { name: "Login" })).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: "Login with OTP" }),
    ).toBeInTheDocument();
  });

  it("renders social login options", () => {
    render(<LoginPage {...mockProps} />);

    expect(
      screen.getByRole("button", { name: /continue with google/i }),
    ).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: /continue with email/i }),
    ).toBeInTheDocument();
  });

  it("renders sign up text", () => {
    render(<LoginPage {...mockProps} />);

    expect(screen.getByText("Don't have an account?")).toBeInTheDocument();
  });

  it("calls onLogin when login button is clicked", () => {
    render(<LoginPage {...mockProps} />);

    const loginButton = screen.getByRole("button", { name: "Login" });
    fireEvent.click(loginButton);

    expect(mockProps.onLogin).toHaveBeenCalledWith(
      "<EMAIL>",
      "password123",
    );
  });

  it("calls onLoginWithOTP when login with OTP button is clicked", () => {
    render(<LoginPage {...mockProps} />);

    const otpButton = screen.getByRole("button", { name: "Login with OTP" });
    fireEvent.click(otpButton);

    expect(mockProps.onLoginWithOTP).toHaveBeenCalled();
  });

  it("calls onForgotPassword when forgot password link is clicked", () => {
    render(<LoginPage {...mockProps} />);

    const forgotPasswordLink = screen.getByText("Forgot Password?");
    fireEvent.click(forgotPasswordLink);

    expect(mockProps.onForgotPassword).toHaveBeenCalled();
  });

  it("calls onContinueWithGoogle when Google button is clicked", () => {
    render(<LoginPage {...mockProps} />);

    const googleButton = screen.getByRole("button", {
      name: /continue with google/i,
    });
    fireEvent.click(googleButton);

    expect(mockProps.onContinueWithGoogle).toHaveBeenCalled();
  });

  it("calls onContinueWithEmail when Email button is clicked", () => {
    render(<LoginPage {...mockProps} />);

    const emailButton = screen.getByRole("button", {
      name: /continue with email/i,
    });
    fireEvent.click(emailButton);

    expect(mockProps.onContinueWithEmail).toHaveBeenCalled();
  });

  it("updates email input value", () => {
    render(<LoginPage {...mockProps} />);

    const emailInput = screen.getByRole("textbox", {
      name: /email or number/i,
    });
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });

    expect(emailInput).toHaveValue("<EMAIL>");
  });

  it("updates password input value", () => {
    render(<LoginPage {...mockProps} />);

    const passwordInput = screen.getByLabelText("Password");
    fireEvent.change(passwordInput, { target: { value: "newpassword" } });

    expect(passwordInput).toHaveValue("newpassword");
  });

  it("toggles remember me checkbox", () => {
    render(<LoginPage {...mockProps} />);

    const rememberMeCheckbox = screen.getByRole("checkbox", {
      name: /remember me/i,
    });
    expect(rememberMeCheckbox).toBeChecked();

    fireEvent.click(rememberMeCheckbox);
    expect(rememberMeCheckbox).not.toBeChecked();
  });

  it("handles form submission", () => {
    render(<LoginPage {...mockProps} />);

    const form = screen
      .getByRole("textbox", { name: /email or number/i })
      .closest("form");
    if (form) {
      fireEvent.submit(form);
      expect(mockProps.onLogin).toHaveBeenCalledWith(
        "<EMAIL>",
        "password123",
      );
    }
  });
});
