import React from "react";
import Image from "next/image";
import Button, { ButtonType } from "../../shared/Button/Button";
import { Check } from "lucide-react";

interface SuccessStepProps {
  successMessage: string;
  onBackToLogin?: () => void;
}

const SuccessStep: React.FC<SuccessStepProps> = ({
  successMessage,
  onBackToLogin,
}) => (
  <div className="text-center flex flex-col gap-6">
    <div className="flex justify-center">
      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
        <Check size={32} className="text-green-600" />
      </div>
    </div>

    <div>
      <h1 className="text-center text-2xl font-semibold text-[var(--grey-7)] mb-2">
        Password Reset Successfully!
        <div className="flex justify-center py-1">
          <Image
            src="/assets/loginPage/Line.png"
            alt="Decorative line"
            className="h-1 w-16"
            width={100}
            height={9}
          />
        </div>
      </h1>
      <p className="text-[var(--grey-6)] text-base font-medium">
        Your password has been reset successfully.
        <br />
        You can now log in with your new password.
      </p>
    </div>

    {successMessage && (
      <div className="text-[var(--green-6)] text-sm text-center bg-green-50 border border-green-200 rounded-sm p-3">
        {successMessage}
      </div>
    )}

    <div className="pt-4">
      <Button
        type={ButtonType.PRIMARY}
        text="Back to Login"
        onClick={onBackToLogin || (() => {})}
      />
    </div>
  </div>
);

export default SuccessStep;
