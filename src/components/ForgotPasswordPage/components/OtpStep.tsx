import React from "react";
import Image from "next/image";
import Button, { ButtonType } from "../../shared/Button/Button";
import OTPInput from "@/components/shared/OTPInput/OTPInput";

interface OtpStepProps {
  localEmail: string;
  otp: string;
  setOtp: (otp: string) => void;
  handleOTPSubmit: (e: React.FormEvent) => void;
  isLoading: boolean;
  errorMessage: string;
  successMessage: string;
  remainingAttempts: number | undefined;
  handleSendOTP: () => void;
  handleVerifyOTP: () => void;
}

const OtpStep: React.FC<OtpStepProps> = ({
  localEmail,
  otp,
  setOtp,
  handleOTPSubmit,
  isLoading,
  errorMessage,
  successMessage,
  remainingAttempts,
  handleSendOTP,
  handleVerifyOTP,
}) => (
  <>
    <div className="text-center flex flex-col">
      <h1 className="text-center pt-[0.563rem] text-2xl font-semibold text-[var(--grey-7)] mb-2">
        Enter Verification Code
        <div className="flex justify-center py-1">
          <Image
            src="/assets/loginPage/Line.png"
            alt="Decorative line"
            className="h-1 w-16"
            width={100}
            height={9}
          />
        </div>
      </h1>
      <p className="text-[var(--grey-6)] text-base font-medium">
        Enter the code sent to your registered email or mobile number.
        <br />
        <span className="font-semibold text-[var(--grey-7)]">{localEmail}</span>
      </p>
    </div>

    <form onSubmit={handleOTPSubmit}>
      <div className="space-y-6">
        <div className="flex flex-col gap-4">
          <label className="block text-base font-medium text-[var(--grey-6)] text-center">
            Enter Verification Code
          </label>
          <OTPInput
            length={6}
            onChange={setOtp}
            onComplete={(code) => setOtp(code)}
            disabled={isLoading}
            error={!!errorMessage}
            autoFocus
          />
        </div>

        {successMessage && (
          <div className="text-[var(--green-6)] text-sm text-center bg-green-50 border border-green-200 rounded-sm p-3">
            {successMessage}
          </div>
        )}

        {errorMessage && (
          <div className="text-[var(--red-6)] text-sm text-center bg-red-50 border border-red-200 rounded-sm p-3">
            {errorMessage}
            {remainingAttempts !== undefined && remainingAttempts > 0 && (
              <div className="mt-1">
                {remainingAttempts} attempt{remainingAttempts !== 1 ? "s" : ""}{" "}
                remaining
              </div>
            )}
          </div>
        )}

        <div className="text-center">
          <button
            type="button"
            onClick={handleSendOTP}
            className="text-[var(--grey-6)] text-medium mb-2 underline"
            disabled={isLoading}
          >
            {isLoading ? "Sending..." : "Resend Code"}
          </button>
        </div>

        <div className="pt-2">
          <Button
            type={ButtonType.PRIMARY}
            text={isLoading ? "Verifying..." : "Continue To Verify Email"}
            onClick={handleVerifyOTP}
            disabled={isLoading || otp.length !== 6}
          />
        </div>
      </div>
    </form>
  </>
);

export default OtpStep;
