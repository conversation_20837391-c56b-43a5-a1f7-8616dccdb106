import React from "react";

interface PasswordStrengthIndicatorProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  newPassword: any;
}

const PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({
  newPassword,
}) => (
  <>
    <div className="flex flex-col gap-2">
      {/* Strength Bar */}
      <div className="w-full h-2 rounded bg-gray-200 overflow-hidden mb-2">
        <div
          style={{
            width: (() => {
              let score = 0;
              if (newPassword.length >= 8) score++;
              if (/[a-z]/.test(newPassword)) score++;
              if (/[A-Z]/.test(newPassword)) score++;
              if (/[@&%$!#^*()]/.test(newPassword)) score++;
              return `${(score / 4) * 100}%`;
            })(),
            background: (() => {
              let score = 0;
              if (newPassword.length >= 8) score++;
              if (/[a-z]/.test(newPassword)) score++;
              if (/[A-Z]/.test(newPassword)) score++;
              if (/[@&%$!#^*()]/.test(newPassword)) score++;
              if (score === 0) return "#e5e7eb"; // gray-200
              if (score === 1) return "#f87171"; // red-400
              if (score === 2) return "#fbbf24"; // yellow-400
              if (score === 3) return "#60a5fa"; // blue-400
              return "#22c55e"; // green-500
            })(),
            height: "100%",
            transition: "width 0.3s, background 0.3s",
          }}
        />
      </div>
      {/* Password Rules */}
      <div className="grid md:grid-cols-2 gap-2">
        <label className="flex items-center gap-2 text-sm">
          <span
            className={`inline-flex items-center justify-center w-5 h-5 border-2 ${
              newPassword.length >= 8
                ? "border-green-600 bg-green-600"
                : "border-gray-300 bg-white"
            } rounded-full transition-colors`}
            aria-hidden="true"
          >
            {newPassword.length >= 8 && (
              <svg
                className="w-3 h-3 text-white"
                fill="none"
                stroke="currentColor"
                strokeWidth="3"
                viewBox="0 0 16 16"
              >
                <path
                  d="M4 8l3 3 5-5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            )}
          </span>
          <span
            className={
              newPassword.length >= 8 ? "text-green-600" : "text-gray-500"
            }
          >
            At least 8 characters
          </span>
        </label>
        <label className="flex items-center gap-2 text-sm">
          <span
            className={`inline-flex items-center justify-center w-5 h-5 border-2 ${
              /[a-z]/.test(newPassword)
                ? "border-green-600 bg-green-600"
                : "border-gray-300 bg-white"
            } rounded-full transition-colors`}
            aria-hidden="true"
          >
            {/[a-z]/.test(newPassword) && (
              <svg
                className="w-3 h-3 text-white"
                fill="none"
                stroke="currentColor"
                strokeWidth="3"
                viewBox="0 0 16 16"
              >
                <path
                  d="M4 8l3 3 5-5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            )}
          </span>
          <span
            className={
              /[a-z]/.test(newPassword) ? "text-green-600" : "text-gray-500"
            }
          >
            1 lowercase letter
          </span>
        </label>
        <label className="flex items-center gap-2 text-sm">
          <span
            className={`inline-flex items-center justify-center w-5 h-5 border-2 ${
              /[A-Z]/.test(newPassword)
                ? "border-green-600 bg-green-600"
                : "border-gray-300 bg-white"
            } rounded-full transition-colors`}
            aria-hidden="true"
          >
            {/[A-Z]/.test(newPassword) && (
              <svg
                className="w-3 h-3 text-white"
                fill="none"
                stroke="currentColor"
                strokeWidth="3"
                viewBox="0 0 16 16"
              >
                <path
                  d="M4 8l3 3 5-5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            )}
          </span>
          <span
            className={
              /[A-Z]/.test(newPassword) ? "text-green-600" : "text-gray-500"
            }
          >
            1 uppercase letter
          </span>
        </label>
        <label className="flex items-center gap-2 text-sm">
          <span
            className={`inline-flex items-center justify-center w-5 h-5 border-2 ${
              /[@&%$!#^*()]/.test(newPassword)
                ? "border-green-600 bg-green-600"
                : "border-gray-300 bg-white"
            } rounded-full transition-colors`}
            aria-hidden="true"
          >
            {/[@&%$!#^*()]/.test(newPassword) && (
              <svg
                className="w-3 h-3 text-white"
                fill="none"
                stroke="currentColor"
                strokeWidth="3"
                viewBox="0 0 16 16"
              >
                <path
                  d="M4 8l3 3 5-5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            )}
          </span>
          <span
            className={
              /[@&%$!#^*()]/.test(newPassword)
                ? "text-green-600"
                : "text-gray-500"
            }
          >
            1 symbol (@ & % $)
          </span>
        </label>
      </div>
    </div>
  </>
);

export default PasswordStrengthIndicator;
