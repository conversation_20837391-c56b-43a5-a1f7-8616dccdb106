import React from "react";
import { Mail, Shield, Check, LockIcon } from "lucide-react";
import { PasswordResetStep } from "../types";

interface StepIndicatorProps {
  currentStep: PasswordResetStep;
}

const StepIndicator: React.FC<StepIndicatorProps> = ({ currentStep }) => {
  const steps = [
    { key: PasswordResetStep.EMAIL_INPUT, label: "Email", icon: Mail },
    { key: PasswordResetStep.OTP_VERIFICATION, label: "Verify", icon: Shield },
    { key: PasswordResetStep.PASSWORD_RESET, label: "Reset", icon: LockIcon },
    { key: PasswordResetStep.SUCCESS, label: "Done", icon: Check },
  ];

  const currentIndex = steps.findIndex((step) => step.key === currentStep);

  return (
    <div className="flex justify-center mb-8">
      <div className="flex items-center space-x-2">
        {steps.map((step, index) => {
          const Icon = step.icon;
          const isActive = index === currentIndex;
          const isCompleted = index < currentIndex;

          return (
            <React.Fragment key={step.key}>
              <div
                className={`
                flex items-center justify-center w-8 h-8 rounded-full
                ${
                  isActive
                    ? "bg-[var(--red-6)] text-white"
                    : isCompleted
                      ? "bg-green-500 text-white"
                      : "bg-gray-200 text-gray-500"
                }
              `}
              >
                <Icon size={16} />
              </div>
              {index < steps.length - 1 && (
                <div
                  className={`w-8 h-0.5 ${
                    isCompleted ? "bg-green-500" : "bg-gray-200"
                  }`}
                />
              )}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};

export default StepIndicator;
