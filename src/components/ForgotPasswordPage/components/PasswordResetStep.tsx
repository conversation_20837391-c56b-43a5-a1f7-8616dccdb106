import React from "react";
import Image from "next/image";
import Button, { ButtonType } from "../../shared/Button/Button";
import PasswordInput from "@/components/shared/PasswordInput";
import PasswordStrengthIndicator from "./PasswordStrengthIndicator";

interface PasswordResetStepProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @next/next/no-img-element
  newPassword: any;
  setNewPassword: (password: string) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @next/next/no-img-element
  confirmPassword: any;
  setConfirmPassword: (password: string) => void;
  handlePasswordSubmit: (e: React.FormEvent) => void;
  isLoading: boolean;
  errorMessage: string;
  handleResetPassword: () => void;
}

const PasswordResetStep: React.FC<PasswordResetStepProps> = ({
  newPassword,
  setNewPassword,
  confirmPassword,
  setConfirmPassword,
  handlePasswordSubmit,
  isLoading,
  errorMessage,
  handleResetPassword,
}) => (
  <>
    <div className="text-center flex flex-col">
      <h1 className="text-center pt-[0.563rem] text-2xl font-semibold text-[var(--grey-7)] mb-2">
        Set New Password
        <div className="flex justify-center py-1">
          <Image
            src="/assets/loginPage/Line.png"
            alt="Decorative line"
            className="h-1 w-16"
            width={100}
            height={9}
          />
        </div>
      </h1>
      <p className="text-[var(--grey-6)] text-base font-medium">
        Enter and confirm your new password to continue using your GIVF account.
      </p>
    </div>

    <form onSubmit={handlePasswordSubmit}>
      <div className="space-y-6">
        <div className="flex flex-col gap-2">
          <label
            htmlFor="newPassword"
            className="block text-base font-medium text-[var(--grey-6)]"
          >
            New Password
          </label>
          <PasswordInput
            id="newPassword"
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            placeholder="Enter new password"
            disabled={isLoading}
            minLength={6}
          />
        </div>

        <div className="flex flex-col gap-2">
          <label
            htmlFor="confirmPassword"
            className="block text-base font-medium text-[var(--grey-6)]"
          >
            Confirm Password
          </label>
          <PasswordInput
            id="confirmPassword"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            placeholder="Confirm new password"
            disabled={isLoading}
            minLength={6}
          />
        </div>

        {/* Password Strength Indicator */}
        <PasswordStrengthIndicator newPassword={newPassword} />

        {newPassword && confirmPassword && newPassword !== confirmPassword && (
          <div className="text-[var(--red-6)] text-sm text-center bg-red-50 border border-red-200 rounded-sm p-3">
            Passwords do not match
          </div>
        )}

        {errorMessage && (
          <div className="text-[var(--red-6)] text-sm text-center bg-red-50 border border-red-200 rounded-sm p-3">
            {errorMessage}
          </div>
        )}

        <div className="pt-2">
          <Button
            type={ButtonType.PRIMARY}
            text={isLoading ? "Resetting..." : "Reset Password"}
            onClick={handleResetPassword}
            disabled={
              isLoading ||
              !newPassword ||
              !confirmPassword ||
              newPassword !== confirmPassword ||
              newPassword.length < 6
            }
          />
        </div>
      </div>
    </form>
  </>
);

export default PasswordResetStep;
