export enum PasswordResetStep {
  EMAIL_INPUT = "email_input",
  OTP_VERIFICATION = "otp_verification",
  PASSWORD_RESET = "password_reset",
  SUCCESS = "success",
}

export interface ForgotPasswordPageProps {
  onSendOTP?: (email: string) => void;
  onVerifyOTP?: (email: string, otp: string) => void;
  onResetPassword?: (email: string, newPassword: string) => void;
  onBackToLogin?: () => void;
  onGetHelp?: () => void;
  currentStep?: PasswordResetStep;
  email?: string;
  isLoading?: boolean;
  successMessage?: string;
  errorMessage?: string;
  remainingAttempts?: number;
}
