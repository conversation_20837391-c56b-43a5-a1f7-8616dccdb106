import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import ForgotPasswordPage from "./ForgotPasswordPage";
import { PasswordResetStep } from "./types";

const meta: Meta<typeof ForgotPasswordPage> = {
  title: "Pages/ForgotPasswordPage",
  component: ForgotPasswordPage,
  tags: ["autodocs"],
  argTypes: {
    currentStep: {
      control: {
        type: "select",
        options: Object.values(PasswordResetStep),
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof ForgotPasswordPage>;

export const EmailInput: Story = {
  args: {
    currentStep: PasswordResetStep.EMAIL_INPUT,
  },
};

export const OTPVerification: Story = {
  args: {
    currentStep: PasswordResetStep.OTP_VERIFICATION,
    email: "<EMAIL>",
  },
};

export const PasswordReset: Story = {
  args: {
    currentStep: PasswordResetStep.PASSWORD_RESET,
    email: "<EMAIL>",
  },
};

export const Success: Story = {
  args: {
    currentStep: PasswordResetStep.SUCCESS,
  },
};

export const WithError: Story = {
  args: {
    currentStep: PasswordResetStep.EMAIL_INPUT,
    errorMessage: "Invalid email address.",
  },
};

export const Loading: Story = {
  args: {
    currentStep: PasswordResetStep.EMAIL_INPUT,
    isLoading: true,
  },
};
