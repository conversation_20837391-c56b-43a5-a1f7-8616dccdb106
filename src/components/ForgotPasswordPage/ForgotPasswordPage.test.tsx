import { render, screen, fireEvent } from "@testing-library/react";
import ForgotPasswordPage from "./ForgotPasswordPage";
import { PasswordResetStep } from "./types";

describe("ForgotPasswordPage", () => {
  it("renders the email input step by default", () => {
    render(<ForgotPasswordPage />);
    expect(
      screen.getByPlaceholderText("Enter your email address")
    ).toBeInTheDocument();
  });

  it("calls onSendOTP when the form is submitted", () => {
    const onSendOTP = jest.fn();
    render(<ForgotPasswordPage onSendOTP={onSendOTP} />);
    fireEvent.change(screen.getByLabelText("Email"), {
      target: { value: "<EMAIL>" },
    });
    fireEvent.click(screen.getByText("Send OTP"));
    expect(onSendOTP).toHaveBeenCalledWith("<EMAIL>");
  });

  it("renders the OTP verification step", () => {
    render(
      <ForgotPasswordPage
        currentStep={PasswordResetStep.OTP_VERIFICATION}
        email="<EMAIL>"
      />
    );
    expect(
      screen.getByRole("heading", { name: /Enter Verification Code/i })
    ).toBeInTheDocument();
  });

  it("calls onVerifyOTP when the OTP is submitted", () => {
    const onVerifyOTP = jest.fn();
    render(
      <ForgotPasswordPage
        currentStep={PasswordResetStep.OTP_VERIFICATION}
        email="<EMAIL>"
        onVerifyOTP={onVerifyOTP}
      />
    );
    const otpInputs = screen.getAllByRole("textbox");
    fireEvent.change(otpInputs[0], { target: { value: "1" } });
    fireEvent.change(otpInputs[1], { target: { value: "2" } });
    fireEvent.change(otpInputs[2], { target: { value: "3" } });
    fireEvent.change(otpInputs[3], { target: { value: "4" } });
    fireEvent.change(otpInputs[4], { target: { value: "5" } });
    fireEvent.change(otpInputs[5], { target: { value: "6" } });

    fireEvent.click(screen.getByText("Continue To Verify Email"));
    expect(onVerifyOTP).toHaveBeenCalledWith("<EMAIL>", "123456");
  });

  it("renders the password reset step", () => {
    render(
      <ForgotPasswordPage
        currentStep={PasswordResetStep.PASSWORD_RESET}
        email="<EMAIL>"
      />
    );
    expect(screen.getByText("Reset Password")).toBeInTheDocument();
  });

  it("calls onResetPassword when the password is submitted", () => {
    const onResetPassword = jest.fn();
    render(
      <ForgotPasswordPage
        currentStep={PasswordResetStep.PASSWORD_RESET}
        email="<EMAIL>"
        onResetPassword={onResetPassword}
      />
    );
    fireEvent.change(screen.getByLabelText("New Password"), {
      target: { value: "password" },
    });
    fireEvent.change(screen.getByLabelText("Confirm Password"), {
      target: { value: "password" },
    });
    fireEvent.click(screen.getByText("Reset Password"));
    expect(onResetPassword).toHaveBeenCalledWith(
      "<EMAIL>",
      "password"
    );
    expect(onResetPassword).toHaveBeenCalledWith(
      "<EMAIL>",
      "password"
    );
  });

  it("renders the success step", () => {
    render(<ForgotPasswordPage currentStep={PasswordResetStep.SUCCESS} />);
    expect(
      screen.getByText("Password Reset Successfully!")
    ).toBeInTheDocument();
  });
});
