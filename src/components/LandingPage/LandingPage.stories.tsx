import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import LandingPage from "./LandingPage";

const meta: Meta<typeof LandingPage> = {
  title: "Pages/LandingPage",
  component: LandingPage,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component:
          "The main landing page component for the IVF success score application. Features a hero section with decorative elements, call-to-action button, and informational content about understanding IVF success chances.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    onStartScore: {
      action: "startScore",
      description:
        "Function called when the 'Start My Score' button is clicked",
    },
    onLogin: {
      action: "login",
      description: "<PERSON><PERSON> called when the login button in header is clicked",
    },
    onPhoneClick: {
      action: "phoneClick",
      description:
        "Function called when a phone number is clicked in the help section",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default Landing Page
export const Default: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story:
          "The default landing page with all interactive elements. Shows the hero section, decorative elements, and call-to-action button.",
      },
    },
  },
};

// With Interactive Handlers
export const WithHandlers: Story = {
  args: {
    onStartScore: () => alert("Starting IVF score assessment..."),
    onLogin: () => alert("Navigating to login page..."),
    onPhoneClick: (phoneNumber: string) => alert(`Calling ${phoneNumber}...`),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Landing page with custom handlers that demonstrate the interactive functionality. Click the buttons to see the alerts.",
      },
    },
  },
};

// Mobile Preview
export const Mobile: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
    docs: {
      description: {
        story:
          "How the landing page appears on mobile devices. The layout is responsive and optimized for smaller screens.",
      },
    },
  },
};

// Tablet Preview
export const Tablet: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "tablet",
    },
    docs: {
      description: {
        story:
          "How the landing page appears on tablet devices. Shows the responsive behavior for medium-sized screens.",
      },
    },
  },
};

// Desktop Preview
export const Desktop: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "desktop",
    },
    docs: {
      description: {
        story:
          "How the landing page appears on desktop devices. This is the optimal viewing experience with full spacing and layout.",
      },
    },
  },
};
