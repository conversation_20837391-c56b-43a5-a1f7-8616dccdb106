// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import "@testing-library/jest-dom";

// Polyfill for Response, Request, and Headers for Next.js API route testing
import "whatwg-fetch";

// Mock the Response global if it's not available
if (typeof global.Response === "undefined") {
  global.Response = Response;
}

if (typeof global.Request === "undefined") {
  global.Request = Request;
}

if (typeof global.Headers === "undefined") {
  global.Headers = Headers;
}
