import { z } from "zod";

// Enum validations matching Prisma schema
export const menstrualRegularitySchema = z.enum(["regular", "irregular"]);

export const infertilityDurationSchema = z.enum([
  "under_6_months",
  "six_to_twelve_months", 
  "over_12_months"
]);

export const dietTypeSchema = z.enum([
  "balanced",
  "vegetarian", 
  "junk_heavy",
  "skipping_meals"
]);

export const exerciseFrequencySchema = z.enum([
  "daily",
  "two_to_three_times",
  "rarely_or_never"
]);

export const incomeRangeSchema = z.enum([
  "under_10k",
  "from_10k_to_50k",
  "from_50k_to_1l",
  "above_1l"
]);

export const livingAreaSchema = z.enum([
  "urban",
  "semi_urban", 
  "rural"
]);

export const stressLevelEnumSchema = z.enum([
  "low",
  "medium",
  "high"
]);

export const pollutionExposureSchema = z.enum([
  "high",
  "moderate",
  "low"
]);

export const occupationTypeSchema = z.enum([
  "desk_job",
  "field_work",
  "night_shift",
  "homemaker"
]);

// Step 1: Biological Factors Schema
export const biologicalFactorsSchema = z.object({
  age: z.number()
    .int("Age must be a whole number")
    .min(18, "Age must be at least 18")
    .max(50, "Age must be 50 or less"),
  height: z.number()
    .min(100, "Height must be at least 100 cm")
    .max(250, "Height must be 250 cm or less"),
  weight: z.number()
    .min(30, "Weight must be at least 30 kg")
    .max(200, "Weight must be 200 kg or less"),
  menstrual_regularity: menstrualRegularitySchema,
  infertility_duration: infertilityDurationSchema,
  ivf_attempts: z.number()
    .int("IVF attempts must be a whole number")
    .min(0, "IVF attempts cannot be negative")
    .max(10, "IVF attempts must be 10 or less"),
  known_conditions: z.string()
    .max(500, "Known conditions description is too long")
    .optional()
    .default("")
});

// Step 2: Lifestyle & Psychosocial Schema
export const lifestylePsychosocialSchema = z.object({
  stress_level: z.number()
    .int("Stress level must be a whole number")
    .min(0, "Stress level must be between 0-10")
    .max(10, "Stress level must be between 0-10"),
  diet_type: dietTypeSchema,
  exercise_frequency: exerciseFrequencySchema,
  sleep_quality: z.number()
    .int("Sleep quality must be a whole number")
    .min(0, "Sleep quality must be between 0-10")
    .max(10, "Sleep quality must be between 0-10"),
  emotional_support_at_home: z.boolean(),
  smoking_or_alcohol_habits: z.string()
    .max(200, "Smoking/alcohol habits description is too long")
    .optional()
    .default("")
});

// Step 3: Environmental & Socioeconomic Schema
export const environmentalSocioeconomicSchema = z.object({
  household_income_range: incomeRangeSchema,
  living_area: livingAreaSchema,
  work_stress_level: stressLevelEnumSchema,
  pollution_exposure: pollutionExposureSchema,
  occupation_type: occupationTypeSchema
});

// Complete IVF Scores Schema (all steps combined)
export const completeIvfScoresSchema = biologicalFactorsSchema
  .merge(lifestylePsychosocialSchema)
  .merge(environmentalSocioeconomicSchema)
  .extend({
    current_step: z.number()
      .int("Current step must be a whole number")
      .min(1, "Current step must be between 1-3")
      .max(3, "Current step must be between 1-3")
      .optional()
      .default(1)
  });

// Partial update schemas for each step
export const updateBiologicalFactorsSchema = biologicalFactorsSchema.partial();
export const updateLifestylePsychosocialSchema = lifestylePsychosocialSchema.partial();
export const updateEnvironmentalSocioeconomicSchema = environmentalSocioeconomicSchema.partial();

// Step progression schema
export const stepProgressionSchema = z.object({
  current_step: z.number()
    .int("Current step must be a whole number")
    .min(1, "Current step must be between 1-3")
    .max(3, "Current step must be between 1-3")
});

// Export types for TypeScript
export type BiologicalFactors = z.infer<typeof biologicalFactorsSchema>;
export type LifestylePsychosocial = z.infer<typeof lifestylePsychosocialSchema>;
export type EnvironmentalSocioeconomic = z.infer<typeof environmentalSocioeconomicSchema>;
export type CompleteIvfScores = z.infer<typeof completeIvfScoresSchema>;
export type UpdateBiologicalFactors = z.infer<typeof updateBiologicalFactorsSchema>;
export type UpdateLifestylePsychosocial = z.infer<typeof updateLifestylePsychosocialSchema>;
export type UpdateEnvironmentalSocioeconomic = z.infer<typeof updateEnvironmentalSocioeconomicSchema>;
export type StepProgression = z.infer<typeof stepProgressionSchema>;
