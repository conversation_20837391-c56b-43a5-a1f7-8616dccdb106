import { createClient, SupabaseClient } from "@supabase/supabase-js";

let serviceRoleSupabase: SupabaseClient | null = null;

export function getServiceRoleSupabase() {
  if (!serviceRoleSupabase) {
    serviceRoleSupabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false, // We'll manage refresh manually
          persistSession: false, // No session persistence in this server-side client
        },
      },
    );
  }
  return serviceRoleSupabase;
}
