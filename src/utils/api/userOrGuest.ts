
import { NextRequest } from "next/server";
import { authenticate } from "@/utils/api/authenticate";
import { getGuestSessionToken, getGuestSession, saveGuestSession } from "@/utils/api/guestSession";

export interface UserContext {
  isAuthenticated: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  user?: any;
  token?: string;
  guestSessionToken?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  guestSession?: any;
}

/**
 * Get user context - either authenticated user or guest session
 * This allows endpoints to work with both authenticated and guest users
 */
export async function getUserOrGuestContext(req: NextRequest): Promise<{
  context: UserContext | null;
  error?: Response;
}> {
  // First try to authenticate the user
  const authResult = await authenticate(req);
  
  if (!authResult.error) {
    // User is authenticated
    return {
      context: {
        isAuthenticated: true,
        user: authResult.user,
        token: authResult.token,
      }
    };
  }

  // User is not authenticated, try guest session
  const guestSessionToken = getGuestSessionToken(req);
  const guestSession = await getGuestSession(guestSessionToken);

  return {
    context: {
      isAuthenticated: false,
      guestSessionToken,
      guestSession,
    }
  };
}

/**
 * Save IVF data for either authenticated user or guest session
 */
export async function saveIVFData(
  context: UserContext,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ivfData: any,
  currentStep: number

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): Promise<{ success: boolean; data?: any; error?: string; guestSessionToken?: string }> {
  try {
    if (context.isAuthenticated && context.user) {
      // Save to authenticated user's record
      const { PrismaClient } = await import("@/generated/prisma");
      const prisma = new PrismaClient();

      const existingScores = await prisma.ivf_scores.findUnique({
        where: { user_id: context.user.id },
      });

      let result;
      if (existingScores) {
        result = await prisma.ivf_scores.update({
          where: { user_id: context.user.id },
          data: {
            ...ivfData,
            current_step: Math.max(existingScores.current_step, currentStep),
          },
        });
      } else {
        // Create with default values for missing steps
        const defaultData = getDefaultIVFData();
        result = await prisma.ivf_scores.create({
          data: {
            user_id: context.user.id,
            ...defaultData,
            ...ivfData,
            current_step: currentStep,
          },
        });
      }

      return { success: true, data: result };
    } else {
      // Save to guest session
      const sessionToken = context.guestSessionToken!;
      
      // Merge with existing guest data if available
      const existingData = context.guestSession?.ivf_data || {};
      const mergedData = { ...existingData, ...ivfData };
      
      const guestSession = await saveGuestSession(
        sessionToken,
        mergedData,
        Math.max(context.guestSession?.current_step || 0, currentStep)
      );

      return { 
        success: true, 
        data: guestSession, 
        guestSessionToken: sessionToken 
      };
    }
  } catch (error) {
    console.error("Error saving IVF data:", error);
    return { success: false, error: "Failed to save data" };
  }
}

/**
 * Get IVF data for either authenticated user or guest session
 */
export async function getIVFData(context: UserContext): Promise<{
  success: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data?: any;
  error?: string;
}> {
  try {
    if (context.isAuthenticated && context.user) {
      // Get from authenticated user's record
      const { PrismaClient } = await import("@/generated/prisma");
      const prisma = new PrismaClient();

      const ivfScores = await prisma.ivf_scores.findUnique({
        where: { user_id: context.user.id },
      });

      return { success: true, data: ivfScores };
    } else {
      // Get from guest session
      if (context.guestSession) {
        return { 
          success: true, 
          data: {
            ...context.guestSession.ivf_data,
            current_step: context.guestSession.current_step,
            id: context.guestSession.id,
            created_at: context.guestSession.created_at,
            updated_at: context.guestSession.updated_at,
          }
        };
      }

      return { success: true, data: null };
    }
  } catch (error) {
    console.error("Error getting IVF data:", error);
    return { success: false, error: "Failed to retrieve data" };
  }
}

/**
 * Get default IVF data structure with required fields
 */
function getDefaultIVFData() {
  return {
    age: 25,
    height: 160,
    weight: 60,
    menstrual_regularity: "regular" as const,
    infertility_duration: "under_6_months" as const,
    ivf_attempts: 0,
    known_conditions: "",
    stress_level: 0,
    diet_type: "balanced" as const,
    exercise_frequency: "rarely_or_never" as const,
    sleep_quality: 0,
    emotional_support_at_home: false,
    smoking_or_alcohol_habits: "",
    household_income_range: "under_10k" as const,
    living_area: "urban" as const,
    work_stress_level: "low" as const,
    pollution_exposure: "low" as const,
    occupation_type: "desk_job" as const,
  };
}
