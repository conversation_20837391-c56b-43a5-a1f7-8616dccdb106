interface OTPData {
  code: string;
  expiresAt: number;
  attempts: number;
}

class OTPStore {
  private store = new Map<string, OTPData>();
  private readonly MAX_ATTEMPTS = 3;
  private readonly EXPIRY_TIME = 5 * 60 * 1000; // 5 minutes

  generateOTP(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  storeOTP(phoneNumber: string, code: string): void {
    const expiresAt = Date.now() + this.EXPIRY_TIME;
    this.store.set(phoneNumber, {
      code,
      expiresAt,
      attempts: 0,
    });
  }

  verifyOTP(
    phoneNumber: string,
    inputCode: string,
  ): {
    success: boolean;
    error?: string;
    remainingAttempts?: number;
  } {
    const otpData = this.store.get(phoneNumber);

    if (!otpData) {
      return {
        success: false,
        error: "No OTP found for this phone number. Please request a new one.",
      };
    }

    // Check if O<PERSON> has expired
    if (Date.now() > otpData.expiresAt) {
      this.store.delete(phoneNumber);
      return {
        success: false,
        error: "O<PERSON> has expired. Please request a new one.",
      };
    }

    // Check if max attempts reached
    if (otpData.attempts >= this.MAX_ATTEMPTS) {
      this.store.delete(phoneNumber);
      return {
        success: false,
        error: "Maximum attempts exceeded. Please request a new OTP.",
      };
    }

    // Increment attempts
    otpData.attempts++;

    // Verify the code
    if (otpData.code !== inputCode) {
      const remainingAttempts = this.MAX_ATTEMPTS - otpData.attempts;

      if (remainingAttempts === 0) {
        this.store.delete(phoneNumber);
        return {
          success: false,
          error:
            "Invalid OTP. Maximum attempts exceeded. Please request a new OTP.",
        };
      }

      return {
        success: false,
        error: "Invalid OTP. Please check your code and try again.",
        remainingAttempts,
      };
    }

    // Success - remove the OTP to prevent reuse
    this.store.delete(phoneNumber);
    return { success: true };
  }

  deleteOTP(phoneNumber: string): void {
    this.store.delete(phoneNumber);
  }

  // Cleanup expired OTPs (call this periodically)
  cleanup(): void {
    const now = Date.now();
    for (const [phoneNumber, otpData] of this.store.entries()) {
      if (now > otpData.expiresAt) {
        this.store.delete(phoneNumber);
      }
    }
  }
}

// Singleton instance
export const otpStore = new OTPStore();

// Optional: Set up periodic cleanup (every 10 minutes)
if (typeof setInterval !== "undefined") {
  setInterval(
    () => {
      otpStore.cleanup();
    },
    10 * 60 * 1000,
  );
}
