interface FakeMessage {
  sid: string;
  body: string;
  from: string;
  to: string;
  status: string;
}

class FakeTwilioClient {
  private accountSid: string;
  private authToken: string;

  constructor(accountSid: string, authToken: string) {
    this.accountSid = accountSid;
    this.authToken = authToken;
  }

  get messages() {
    return {
      create: async (params: {
        body: string;
        from: string;
        to: string;
      }): Promise<FakeMessage> => {
        // Simulate a slight delay like a real API call
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Generate a fake message SID
        const sid = `SM${Math.random().toString(36).substring(2, 15)}`;

        console.log(
          `[FAKE TWILIO] Would send SMS to ${params.to}: ${params.body}`,
        );

        return {
          sid,
          body: params.body,
          from: params.from,
          to: params.to,
          status: "sent",
        };
      },
    };
  }
}

export function createFakeTwilioClient(
  accountSid: string,
  authToken: string,
): FakeTwilioClient {
  return new FakeTwilioClient(accountSid, authToken);
}

// Mock function that matches <PERSON><PERSON><PERSON>'s interface
export default function fakeTwilio(
  accountSid: string,
  authToken: string,
): FakeTwilioClient {
  return new FakeTwilioClient(accountSid, authToken);
}
