import { PrismaClient } from "@/generated/prisma";

interface OTPVerificationResult {
  success: boolean;
  error?: string;
  remainingAttempts?: number;
}

class DatabaseOTPStore {
  private prisma: PrismaClient;
  private readonly MAX_ATTEMPTS = 3;
  private readonly EXPIRY_TIME = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.prisma = new PrismaClient();
  }

  generateOTP(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  async storeOTP(phoneNumber: string, code: string): Promise<void> {
    const expiresAt = new Date(Date.now() + this.EXPIRY_TIME);

    // Delete any existing OTP for this phone number first
    await this.prisma.otps.deleteMany({
      where: { phone_number: phoneNumber },
    });

    // Create new OTP record
    await this.prisma.otps.create({
      data: {
        phone_number: phoneNumber,
        code,
        expires_at: expiresAt,
        attempts: 0,
        is_verified: false,
      },
    });
  }

  async verifyOTP(
    phoneNumber: string,
    inputCode: string,
  ): Promise<OTPVerificationResult> {
    try {
      // Find the OTP record
      const otpRecord = await this.prisma.otps.findUnique({
        where: { phone_number: phoneNumber },
      });

      if (!otpRecord) {
        return {
          success: false,
          error:
            "No OTP found for this phone number. Please request a new one.",
        };
      }

      // Check if already verified
      if (otpRecord.is_verified) {
        return {
          success: false,
          error: "This OTP has already been used. Please request a new one.",
        };
      }

      // Check if OTP has expired
      if (new Date() > otpRecord.expires_at) {
        await this.deleteOTP(phoneNumber);
        return {
          success: false,
          error: "OTP has expired. Please request a new one.",
        };
      }

      // Check if max attempts reached
      if (otpRecord.attempts >= this.MAX_ATTEMPTS) {
        await this.deleteOTP(phoneNumber);
        return {
          success: false,
          error: "Maximum attempts exceeded. Please request a new OTP.",
        };
      }

      // Increment attempts
      const updatedRecord = await this.prisma.otps.update({
        where: { phone_number: phoneNumber },
        data: { attempts: otpRecord.attempts + 1 },
      });

      // Verify the code
      if (otpRecord.code !== inputCode) {
        const remainingAttempts = this.MAX_ATTEMPTS - updatedRecord.attempts;

        if (remainingAttempts === 0) {
          await this.deleteOTP(phoneNumber);
          return {
            success: false,
            error:
              "Invalid OTP. Maximum attempts exceeded. Please request a new OTP.",
          };
        }

        return {
          success: false,
          error: "Invalid OTP. Please check your code and try again.",
          remainingAttempts,
        };
      }

      // Success - mark as verified and delete
      await this.prisma.otps.update({
        where: { phone_number: phoneNumber },
        data: { is_verified: true },
      });

      // Optionally delete the record after successful verification
      // Or keep it for audit purposes with is_verified = true
      await this.deleteOTP(phoneNumber);

      return { success: true };
    } catch (error) {
      console.error("Database error during OTP verification:", error);
      return {
        success: false,
        error: "An error occurred during verification. Please try again.",
      };
    }
  }

  async deleteOTP(phoneNumber: string): Promise<void> {
    try {
      await this.prisma.otps.deleteMany({
        where: { phone_number: phoneNumber },
      });
    } catch (error) {
      console.error("Error deleting OTP:", error);
    }
  }

  // Cleanup expired OTPs (call this periodically or via cron job)
  async cleanup(): Promise<void> {
    try {
      const now = new Date();
      const result = await this.prisma.otps.deleteMany({
        where: {
          expires_at: {
            lt: now,
          },
        },
      });
      console.log(`Cleaned up ${result.count} expired OTPs`);
    } catch (error) {
      console.error("Error during OTP cleanup:", error);
    }
  }

  // Get OTP statistics (useful for monitoring)
  async getStats(): Promise<{
    total: number;
    expired: number;
    verified: number;
  }> {
    try {
      const now = new Date();
      const [total, expired, verified] = await Promise.all([
        this.prisma.otps.count(),
        this.prisma.otps.count({
          where: { expires_at: { lt: now } },
        }),
        this.prisma.otps.count({
          where: { is_verified: true },
        }),
      ]);

      return { total, expired, verified };
    } catch (error) {
      console.error("Error getting OTP stats:", error);
      return { total: 0, expired: 0, verified: 0 };
    }
  }

  // Close database connection
  async disconnect(): Promise<void> {
    await this.prisma.$disconnect();
  }
}

// Singleton instance
export const dbOtpStore = new DatabaseOTPStore();

// Optional: Set up periodic cleanup (every 10 minutes)
// In production, consider using a cron job instead
if (typeof setInterval !== "undefined") {
  setInterval(
    async () => {
      await dbOtpStore.cleanup();
    },
    10 * 60 * 1000,
  );
}
