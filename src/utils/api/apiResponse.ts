/**
 * Helper to create a consistent API Response.
 * @param status HTTP status code
 * @param message Message string (optional)
 * @param data Any additional data (optional)
 */

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function apiResponse(status: number, message?: string, data?: any) {
  const body = { ...(message && { error: message }), ...(data && data) };
  return new Response(
    JSON.stringify(body),
    {
      status,
      headers: { "Content-Type": "application/json" },
    }
  );
}
