import { NextRequest } from "next/server";
import { parseToken, verifyAccessToken } from "@/utils/api/verifyToken";
import { apiResponse } from "@/utils/api/apiResponse";

/**
 * Auth utility for Next.js API routes.
 * Returns { user, token } if authorized, otherwise { error } with error Response.
 */
export async function authenticate(req: NextRequest) {
  const token = parseToken(req);
  if (!token) {
    return {
      error: apiResponse(401, "Unauthorized: No token provided"),
    };
  }

  const user = await verifyAccessToken(token);
  if (!user) {
    return {
      error: apiResponse(401, "Unauthorized: Invalid token"),
    };
  }

  return { user, token };
}
