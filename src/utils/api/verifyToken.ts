// utils/verifyToken.ts
import { NextRequest } from "next/server";
import { getServiceRoleSupabase } from "@/utils/supabase/service-role"; // Use the service role client

// Type for the parsed user from Supabase
import { User } from "@supabase/supabase-js";

// Helper function to extract token from Authorization header
export const parseToken = (req: NextRequest): string | null => {
  const authHeader =
    req.headers.get("authorization") || req.headers.get("Authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return null;
  }
  return authHeader.split(" ")[1];
};

// Helper function to verify token using the service role key
export const verifyAccessToken = async (
  token: string,
): Promise<User | null> => {
  const supabase = getServiceRoleSupabase();

  // Using getUser with service_role_key can verify a token
  // but remember it won't enforce RLS on subsequent database calls made with *this* client.
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser(token);

  if (error) {
    console.error("Token verification error (service role):", error.message);
    return null;
  }
  return user;
};
