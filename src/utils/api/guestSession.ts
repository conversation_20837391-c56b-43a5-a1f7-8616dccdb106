import { NextRequest } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { randomUUID } from "crypto";

const prisma = new PrismaClient();

// Guest session data structure
export interface GuestSession {
  id: string;
  session_token: string;
  ivf_data: any;
  current_step: number;
  email?: string;
  is_verified: boolean;
  expires_at: Date;
  created_at: Date;
  updated_at: Date;
}

/**
 * Get or create a guest session token from request headers
 */
export function getGuestSessionToken(req: NextRequest): string {
  // Check for existing guest session token in headers
  const existingToken = req.headers.get("x-guest-session") || req.headers.get("X-Guest-Session");
  
  if (existingToken) {
    return existingToken;
  }
  
  // Generate new guest session token
  return `guest_${randomUUID()}`;
}

/**
 * Create or update guest session data
 */
export async function saveGuestSession(
  sessionToken: string,
  ivfData: any,
  currentStep: number,
  email?: string
): Promise<GuestSession> {
  const expiresAt = new Date();
  expiresAt.setHours(expiresAt.getHours() + 24); // 24 hour expiry

  // Try to update existing session first
  try {
    const existingSession = await prisma.$queryRaw<GuestSession[]>`
      SELECT * FROM guest_sessions WHERE session_token = ${sessionToken} LIMIT 1
    `;

    if (existingSession.length > 0) {
      const updated = await prisma.$queryRaw<GuestSession[]>`
        UPDATE guest_sessions 
        SET ivf_data = ${JSON.stringify(ivfData)}, 
            current_step = ${currentStep},
            email = ${email || null},
            expires_at = ${expiresAt},
            updated_at = NOW()
        WHERE session_token = ${sessionToken}
        RETURNING *
      `;
      return updated[0];
    }
  } catch (error) {
    // Table might not exist, we'll create it below
    console.log("Guest sessions table might not exist, creating session in memory");
  }

  // For now, we'll use a simple in-memory store since we don't have the guest_sessions table
  // In production, you'd want to create a proper guest_sessions table
  const guestSession: GuestSession = {
    id: randomUUID(),
    session_token: sessionToken,
    ivf_data: ivfData,
    current_step: currentStep,
    email,
    is_verified: false,
    expires_at: expiresAt,
    created_at: new Date(),
    updated_at: new Date(),
  };

  // Store in a simple cache (in production, use Redis or database)
  guestSessionCache.set(sessionToken, guestSession);
  
  return guestSession;
}

/**
 * Get guest session data
 */
export async function getGuestSession(sessionToken: string): Promise<GuestSession | null> {
  try {
    // Try database first
    const sessions = await prisma.$queryRaw<GuestSession[]>`
      SELECT * FROM guest_sessions 
      WHERE session_token = ${sessionToken} 
      AND expires_at > NOW() 
      LIMIT 1
    `;

    if (sessions.length > 0) {
      return sessions[0];
    }
  } catch (error) {
    // Fall back to cache
    console.log("Using cache for guest session");
  }

  // Check cache
  const cachedSession = guestSessionCache.get(sessionToken);
  if (cachedSession && cachedSession.expires_at > new Date()) {
    return cachedSession;
  }

  return null;
}

/**
 * Convert guest session to authenticated user data
 */
export async function convertGuestToUser(sessionToken: string, userId: string): Promise<boolean> {
  try {
    const guestSession = await getGuestSession(sessionToken);
    if (!guestSession || !guestSession.is_verified) {
      return false;
    }

    // Create IVF scores for the authenticated user
    await prisma.ivf_scores.create({
      data: {
        user_id: userId,
        ...guestSession.ivf_data,
        current_step: guestSession.current_step,
      },
    });

    // Clean up guest session
    try {
      await prisma.$queryRaw`
        DELETE FROM guest_sessions WHERE session_token = ${sessionToken}
      `;
    } catch (error) {
      // Remove from cache if database cleanup fails
      guestSessionCache.delete(sessionToken);
    }

    return true;
  } catch (error) {
    console.error("Error converting guest to user:", error);
    return false;
  }
}

/**
 * Verify guest email and mark session as verified
 */
export async function verifyGuestEmail(sessionToken: string, email: string): Promise<boolean> {
  try {
    const guestSession = await getGuestSession(sessionToken);
    if (!guestSession || guestSession.email !== email) {
      return false;
    }

    // Mark as verified
    try {
      await prisma.$queryRaw`
        UPDATE guest_sessions 
        SET is_verified = true, updated_at = NOW()
        WHERE session_token = ${sessionToken}
      `;
    } catch (error) {
      // Update cache if database update fails
      const cachedSession = guestSessionCache.get(sessionToken);
      if (cachedSession) {
        cachedSession.is_verified = true;
        cachedSession.updated_at = new Date();
        guestSessionCache.set(sessionToken, cachedSession);
      }
    }

    return true;
  } catch (error) {
    console.error("Error verifying guest email:", error);
    return false;
  }
}

// Simple in-memory cache for guest sessions (use Redis in production)
const guestSessionCache = new Map<string, GuestSession>();

// Clean up expired sessions every hour
setInterval(() => {
  const now = new Date();
  for (const [token, session] of guestSessionCache.entries()) {
    if (session.expires_at <= now) {
      guestSessionCache.delete(token);
    }
  }
}, 60 * 60 * 1000); // 1 hour
