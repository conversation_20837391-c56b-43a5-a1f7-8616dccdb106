/**
 * Example React component showing the new guest-friendly IVF Scores flow
 * 
 * This demonstrates:
 * 1. Guest users can complete Steps 1-3 without authentication
 * 2. Email verification is required after Step 3 for guests
 * 3. Results are only accessible after email verification or authentication
 */

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  biologicalFactorsSchema,
  type BiologicalFactors,
} from "@/validations/ivf-scores";

// Guest session management
class GuestSessionManager {
  private static TOKEN_KEY = "guest_session_token";

  static getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  static setToken(token: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
  }

  static clearToken(): void {
    localStorage.removeItem(this.TOKEN_KEY);
  }

  static getHeaders(): Record<string, string> {
    const token = this.getToken();
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };
    
    if (token) {
      headers["X-Guest-Session"] = token;
    }
    
    return headers;
  }

  static handleResponse(response: Response): void {
    const guestToken = response.headers.get("X-Guest-Session");
    if (guestToken) {
      this.setToken(guestToken);
    }
  }
}

// Progress tracker component
export function IVFProgressTracker() {
  const [progress, setProgress] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchProgress();
  }, []);

  const fetchProgress = async () => {
    try {
      const response = await fetch("/api/v1/ivf-scores/progress", {
        headers: GuestSessionManager.getHeaders(),
      });

      if (response.ok) {
        const data = await response.json();
        setProgress(data);
      }
    } catch (error) {
      console.error("Error fetching progress:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>Loading progress...</div>;

  return (
    <div className="bg-gray-100 p-4 rounded">
      <h3 className="text-lg font-semibold mb-2">IVF Scores Progress</h3>
      
      {progress?.hasStarted ? (
        <div>
          <div className="mb-2">
            <div className="bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full"
                style={{ width: `${progress.progressPercentage}%` }}
              />
            </div>
            <p className="text-sm mt-1">{progress.progressPercentage}% Complete</p>
          </div>
          
          <p>Current Step: {progress.currentStep} - {progress.stepDescriptions[progress.currentStep]}</p>
          
          {progress.nextStep && (
            <p>Next Step: {progress.nextStep} - {progress.stepDescriptions[progress.nextStep]}</p>
          )}
          
          {progress.isCompleted && progress.isGuest && progress.nextAction === "email_verification" && (
            <div className="mt-2 p-2 bg-yellow-100 rounded">
              <p className="text-yellow-800 font-semibold">
                ✅ All steps completed! Please verify your email to view results.
              </p>
            </div>
          )}
          
          {progress.isCompleted && !progress.isGuest && (
            <p className="text-green-600 font-semibold">✅ All steps completed! You can view your results.</p>
          )}

          {progress.isGuest && (
            <p className="text-sm text-gray-600 mt-2">
              💡 You're completing this as a guest. No account required!
            </p>
          )}
        </div>
      ) : (
        <p>IVF scoring process not started yet.</p>
      )}
    </div>
  );
}

// Step 1 form for guests and authenticated users
export function Step1GuestFriendlyForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState("");

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<BiologicalFactors>({
    resolver: zodResolver(biologicalFactorsSchema),
  });

  const onSubmit = async (data: BiologicalFactors) => {
    setIsSubmitting(true);
    setMessage("");

    try {
      const response = await fetch("/api/v1/ivf-scores/step1", {
        method: "POST",
        headers: GuestSessionManager.getHeaders(),
        body: JSON.stringify(data),
      });

      // Handle guest session token
      GuestSessionManager.handleResponse(response);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to save Step 1 data");
      }

      const result = await response.json();
      console.log("Step 1 completed:", result);
      
      if (result.isGuest) {
        setMessage("Step 1 completed! No account required. Proceeding to Step 2...");
      } else {
        setMessage("Step 1 completed! Proceeding to Step 2...");
      }
      
      // Navigate to Step 2 or show success message
    } catch (error) {
      console.error("Error saving Step 1:", error);
      setMessage(`Error: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <h2 className="text-xl font-bold">Step 1: Biological Factors</h2>
      <p className="text-sm text-gray-600">No account required - complete as a guest!</p>
      
      <div>
        <label htmlFor="age">Age</label>
        <input
          id="age"
          type="number"
          {...register("age", { valueAsNumber: true })}
          className="border rounded px-3 py-2"
        />
        {errors.age && <p className="text-red-500">{errors.age.message}</p>}
      </div>

      <div>
        <label htmlFor="height">Height (cm)</label>
        <input
          id="height"
          type="number"
          step="0.1"
          {...register("height", { valueAsNumber: true })}
          className="border rounded px-3 py-2"
        />
        {errors.height && <p className="text-red-500">{errors.height.message}</p>}
      </div>

      <div>
        <label htmlFor="weight">Weight (kg)</label>
        <input
          id="weight"
          type="number"
          step="0.1"
          {...register("weight", { valueAsNumber: true })}
          className="border rounded px-3 py-2"
        />
        {errors.weight && <p className="text-red-500">{errors.weight.message}</p>}
      </div>

      <div>
        <label htmlFor="menstrual_regularity">Menstrual Regularity</label>
        <select
          id="menstrual_regularity"
          {...register("menstrual_regularity")}
          className="border rounded px-3 py-2"
        >
          <option value="">Select...</option>
          <option value="regular">Regular</option>
          <option value="irregular">Irregular</option>
        </select>
        {errors.menstrual_regularity && (
          <p className="text-red-500">{errors.menstrual_regularity.message}</p>
        )}
      </div>

      <div>
        <label htmlFor="infertility_duration">Infertility Duration</label>
        <select
          id="infertility_duration"
          {...register("infertility_duration")}
          className="border rounded px-3 py-2"
        >
          <option value="">Select...</option>
          <option value="under_6_months">Under 6 months</option>
          <option value="six_to_twelve_months">6-12 months</option>
          <option value="over_12_months">Over 12 months</option>
        </select>
        {errors.infertility_duration && (
          <p className="text-red-500">{errors.infertility_duration.message}</p>
        )}
      </div>

      <div>
        <label htmlFor="ivf_attempts">Previous IVF Attempts</label>
        <input
          id="ivf_attempts"
          type="number"
          {...register("ivf_attempts", { valueAsNumber: true })}
          className="border rounded px-3 py-2"
        />
        {errors.ivf_attempts && (
          <p className="text-red-500">{errors.ivf_attempts.message}</p>
        )}
      </div>

      <div>
        <label htmlFor="known_conditions">Known Medical Conditions</label>
        <textarea
          id="known_conditions"
          {...register("known_conditions")}
          className="border rounded px-3 py-2"
          rows={3}
        />
        {errors.known_conditions && (
          <p className="text-red-500">{errors.known_conditions.message}</p>
        )}
      </div>

      {message && (
        <div className={`p-2 rounded ${message.includes("Error") ? "bg-red-100 text-red-700" : "bg-green-100 text-green-700"}`}>
          {message}
        </div>
      )}

      <button
        type="submit"
        disabled={isSubmitting}
        className="bg-blue-500 text-white px-4 py-2 rounded disabled:opacity-50"
      >
        {isSubmitting ? "Saving..." : "Save Step 1 (No Account Required)"}
      </button>
    </form>
  );
}

// Email verification component for guests
export function EmailVerificationForm() {
  const [email, setEmail] = useState("");
  const [code, setCode] = useState("");
  const [step, setStep] = useState<"email" | "code">("email");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState("");

  const sendVerificationEmail = async () => {
    setIsSubmitting(true);
    setMessage("");

    try {
      const response = await fetch("/api/v1/ivf-scores/verify-email", {
        method: "POST",
        headers: GuestSessionManager.getHeaders(),
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to send verification email");
      }

      const result = await response.json();
      setMessage("Verification email sent! Please check your email for the code.");
      setStep("code");
      
      // In development, show the code
      if (result.developmentCode) {
        setMessage(`Verification email sent! Development code: ${result.developmentCode}`);
      }
    } catch (error) {
      setMessage(`Error: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const verifyCode = async () => {
    setIsSubmitting(true);
    setMessage("");

    try {
      const response = await fetch("/api/v1/ivf-scores/verify-email", {
        method: "PUT",
        headers: GuestSessionManager.getHeaders(),
        body: JSON.stringify({ email, code }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to verify code");
      }

      const result = await response.json();
      setMessage("Email verified successfully! You can now view your IVF score results.");
      
      // Redirect to results page or show results
    } catch (error) {
      setMessage(`Error: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-bold">Email Verification Required</h2>
      <p className="text-gray-600">
        You've completed all three steps! Please verify your email to view your IVF score results.
      </p>

      {step === "email" ? (
        <div className="space-y-4">
          <div>
            <label htmlFor="email">Email Address</label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="border rounded px-3 py-2 w-full"
              placeholder="Enter your email address"
            />
          </div>

          <button
            onClick={sendVerificationEmail}
            disabled={isSubmitting || !email}
            className="bg-blue-500 text-white px-4 py-2 rounded disabled:opacity-50"
          >
            {isSubmitting ? "Sending..." : "Send Verification Email"}
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          <div>
            <label htmlFor="code">Verification Code</label>
            <input
              id="code"
              type="text"
              value={code}
              onChange={(e) => setCode(e.target.value)}
              className="border rounded px-3 py-2 w-full"
              placeholder="Enter 6-digit code"
              maxLength={6}
            />
          </div>

          <div className="flex space-x-2">
            <button
              onClick={verifyCode}
              disabled={isSubmitting || code.length !== 6}
              className="bg-green-500 text-white px-4 py-2 rounded disabled:opacity-50"
            >
              {isSubmitting ? "Verifying..." : "Verify Code"}
            </button>

            <button
              onClick={() => setStep("email")}
              className="bg-gray-500 text-white px-4 py-2 rounded"
            >
              Change Email
            </button>
          </div>
        </div>
      )}

      {message && (
        <div className={`p-2 rounded ${message.includes("Error") ? "bg-red-100 text-red-700" : "bg-green-100 text-green-700"}`}>
          {message}
        </div>
      )}
    </div>
  );
}
