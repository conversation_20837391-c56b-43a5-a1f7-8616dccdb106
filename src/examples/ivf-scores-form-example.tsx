/**
 * Example React component showing how to use the IVF Scores validation schemas
 * with React Hook Form for client-side validation.
 * 
 * This file demonstrates the reusability of the validation schemas created
 * in /src/validations/ivf-scores.ts
 */

import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  biologicalFactorsSchema,
  lifestylePsychosocialSchema,
  type BiologicalFactors,
  type LifestylePsychosocial,
} from "@/validations/ivf-scores";

// Example Step 1 Form Component
export function Step1BiologicalFactorsForm() {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<BiologicalFactors>({
    resolver: zodResolver(biologicalFactorsSchema),
  });

  const onSubmit = async (data: BiologicalFactors) => {
    try {
      const response = await fetch("/api/v1/ivf-scores/step1", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${localStorage.getItem("access_token")}`,
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to save Step 1 data");
      }

      const result = await response.json();
      console.log("Step 1 completed:", result);
      // Navigate to Step 2 or show success message
    } catch (error) {
      console.error("Error saving Step 1:", error);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <h2 className="text-xl font-bold">Step 1: Biological Factors</h2>
      
      <div>
        <label htmlFor="age">Age</label>
        <input
          id="age"
          type="number"
          {...register("age", { valueAsNumber: true })}
          className="border rounded px-3 py-2"
        />
        {errors.age && <p className="text-red-500">{errors.age.message}</p>}
      </div>

      <div>
        <label htmlFor="height">Height (cm)</label>
        <input
          id="height"
          type="number"
          step="0.1"
          {...register("height", { valueAsNumber: true })}
          className="border rounded px-3 py-2"
        />
        {errors.height && <p className="text-red-500">{errors.height.message}</p>}
      </div>

      <div>
        <label htmlFor="weight">Weight (kg)</label>
        <input
          id="weight"
          type="number"
          step="0.1"
          {...register("weight", { valueAsNumber: true })}
          className="border rounded px-3 py-2"
        />
        {errors.weight && <p className="text-red-500">{errors.weight.message}</p>}
      </div>

      <div>
        <label htmlFor="menstrual_regularity">Menstrual Regularity</label>
        <select
          id="menstrual_regularity"
          {...register("menstrual_regularity")}
          className="border rounded px-3 py-2"
        >
          <option value="">Select...</option>
          <option value="regular">Regular</option>
          <option value="irregular">Irregular</option>
        </select>
        {errors.menstrual_regularity && (
          <p className="text-red-500">{errors.menstrual_regularity.message}</p>
        )}
      </div>

      <div>
        <label htmlFor="infertility_duration">Infertility Duration</label>
        <select
          id="infertility_duration"
          {...register("infertility_duration")}
          className="border rounded px-3 py-2"
        >
          <option value="">Select...</option>
          <option value="under_6_months">Under 6 months</option>
          <option value="six_to_twelve_months">6-12 months</option>
          <option value="over_12_months">Over 12 months</option>
        </select>
        {errors.infertility_duration && (
          <p className="text-red-500">{errors.infertility_duration.message}</p>
        )}
      </div>

      <div>
        <label htmlFor="ivf_attempts">Previous IVF Attempts</label>
        <input
          id="ivf_attempts"
          type="number"
          {...register("ivf_attempts", { valueAsNumber: true })}
          className="border rounded px-3 py-2"
        />
        {errors.ivf_attempts && (
          <p className="text-red-500">{errors.ivf_attempts.message}</p>
        )}
      </div>

      <div>
        <label htmlFor="known_conditions">Known Medical Conditions</label>
        <textarea
          id="known_conditions"
          {...register("known_conditions")}
          className="border rounded px-3 py-2"
          rows={3}
        />
        {errors.known_conditions && (
          <p className="text-red-500">{errors.known_conditions.message}</p>
        )}
      </div>

      <button
        type="submit"
        disabled={isSubmitting}
        className="bg-blue-500 text-white px-4 py-2 rounded disabled:opacity-50"
      >
        {isSubmitting ? "Saving..." : "Save Step 1"}
      </button>
    </form>
  );
}

// Example Step 2 Form Component
export function Step2LifestylePsychosocialForm() {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LifestylePsychosocial>({
    resolver: zodResolver(lifestylePsychosocialSchema),
  });

  const onSubmit = async (data: LifestylePsychosocial) => {
    try {
      const response = await fetch("/api/v1/ivf-scores/step2", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${localStorage.getItem("access_token")}`,
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to save Step 2 data");
      }

      const result = await response.json();
      console.log("Step 2 completed:", result);
      // Navigate to Step 3 or show success message
    } catch (error) {
      console.error("Error saving Step 2:", error);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <h2 className="text-xl font-bold">Step 2: Lifestyle & Psychosocial</h2>
      
      <div>
        <label htmlFor="stress_level">Stress Level (0-10)</label>
        <input
          id="stress_level"
          type="number"
          min="0"
          max="10"
          {...register("stress_level", { valueAsNumber: true })}
          className="border rounded px-3 py-2"
        />
        {errors.stress_level && (
          <p className="text-red-500">{errors.stress_level.message}</p>
        )}
      </div>

      <div>
        <label htmlFor="diet_type">Diet Type</label>
        <select
          id="diet_type"
          {...register("diet_type")}
          className="border rounded px-3 py-2"
        >
          <option value="">Select...</option>
          <option value="balanced">Balanced</option>
          <option value="vegetarian">Vegetarian</option>
          <option value="junk_heavy">Junk Heavy</option>
          <option value="skipping_meals">Skipping Meals</option>
        </select>
        {errors.diet_type && (
          <p className="text-red-500">{errors.diet_type.message}</p>
        )}
      </div>

      <div>
        <label htmlFor="exercise_frequency">Exercise Frequency</label>
        <select
          id="exercise_frequency"
          {...register("exercise_frequency")}
          className="border rounded px-3 py-2"
        >
          <option value="">Select...</option>
          <option value="daily">Daily</option>
          <option value="two_to_three_times">2-3 times per week</option>
          <option value="rarely_or_never">Rarely or never</option>
        </select>
        {errors.exercise_frequency && (
          <p className="text-red-500">{errors.exercise_frequency.message}</p>
        )}
      </div>

      <div>
        <label htmlFor="sleep_quality">Sleep Quality (0-10)</label>
        <input
          id="sleep_quality"
          type="number"
          min="0"
          max="10"
          {...register("sleep_quality", { valueAsNumber: true })}
          className="border rounded px-3 py-2"
        />
        {errors.sleep_quality && (
          <p className="text-red-500">{errors.sleep_quality.message}</p>
        )}
      </div>

      <div>
        <label htmlFor="emotional_support_at_home">
          <input
            id="emotional_support_at_home"
            type="checkbox"
            {...register("emotional_support_at_home")}
            className="mr-2"
          />
          I have emotional support at home
        </label>
        {errors.emotional_support_at_home && (
          <p className="text-red-500">{errors.emotional_support_at_home.message}</p>
        )}
      </div>

      <div>
        <label htmlFor="smoking_or_alcohol_habits">Smoking or Alcohol Habits</label>
        <textarea
          id="smoking_or_alcohol_habits"
          {...register("smoking_or_alcohol_habits")}
          className="border rounded px-3 py-2"
          rows={2}
        />
        {errors.smoking_or_alcohol_habits && (
          <p className="text-red-500">{errors.smoking_or_alcohol_habits.message}</p>
        )}
      </div>

      <button
        type="submit"
        disabled={isSubmitting}
        className="bg-blue-500 text-white px-4 py-2 rounded disabled:opacity-50"
      >
        {isSubmitting ? "Saving..." : "Save Step 2"}
      </button>
    </form>
  );
}

// Example Progress Tracker Component
export function IVFScoresProgress() {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [progress, setProgress] = React.useState<any>(null);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    const fetchProgress = async () => {
      try {
        const response = await fetch("/api/v1/ivf-scores/progress", {
          headers: {
            "Authorization": `Bearer ${localStorage.getItem("access_token")}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          setProgress(data);
        }
      } catch (error) {
        console.error("Error fetching progress:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchProgress();
  }, []);

  if (loading) return <div>Loading progress...</div>;

  return (
    <div className="bg-gray-100 p-4 rounded">
      <h3 className="text-lg font-semibold mb-2">IVF Scores Progress</h3>
      
      {progress?.hasStarted ? (
        <div>
          <div className="mb-2">
            <div className="bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full"
                style={{ width: `${progress.progressPercentage}%` }}
              />
            </div>
            <p className="text-sm mt-1">{progress.progressPercentage}% Complete</p>
          </div>
          
          <p>Current Step: {progress.currentStep} - {progress.stepDescriptions[progress.currentStep]}</p>
          
          {progress.nextStep && (
            <p>Next Step: {progress.nextStep} - {progress.stepDescriptions[progress.nextStep]}</p>
          )}
          
          {progress.isCompleted && (
            <p className="text-green-600 font-semibold">✅ All steps completed!</p>
          )}
        </div>
      ) : (
        <p>IVF scoring process not started yet.</p>
      )}
    </div>
  );
}
