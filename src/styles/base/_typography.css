@import "styles/abstracts/variables";

html,
body {
  font-family: var(--font-base);
}

p {
  font-size: 1rem;
  font-family: var(--font-base);
  line-height: 1.375rem;
}

a {
  color: inherit;
  text-decoration: none;
}

h1 {
  font-size: 4.375rem;
  line-height: 5.875rem;
  letter-spacing: 0;
  font-weight: var(--font-weight-black);
}

h2 {
  font-size: 3.125rem;
  line-height: 100%;
  letter-spacing: 0;
  font-weight: var(--font-weight-black);
}

h3 {
  font-size: 2.5rem;
  line-height: 3.875rem;
  letter-spacing: 0;
  font-weight: var(--font-weight-bold);
}

h4 {
  font-size: 2rem;
  line-height: 100%;
  letter-spacing: 0;
  font-weight: var(--font-weight-bold);
}

ul,
ol {
  margin-left: 2rem;
  line-height: 1.375rem;
}

blockquote {
  padding-left: 1rem;
  border-left: 3px solid #007bff;
}
