/** @type {import('jest').Config} */
module.exports = {
  projects: [
    // Frontend tests (React components, pages, hooks)
    {
      displayName: "client",
      testEnvironment: "jsdom",
      testMatch: [
        "<rootDir>/src/components/**/*.test.{ts,tsx}",
        "<rootDir>/src/app/**/page.test.{ts,tsx}",
        "<rootDir>/src/app/**/layout.test.{ts,tsx}",
        "<rootDir>/src/hooks/**/*.test.{ts,tsx}",
        "<rootDir>/src/app/**/*.client.test.{ts,tsx}",
      ],
      moduleFileExtensions: ["ts", "tsx", "js", "jsx", "json", "node"],
      transform: {
        "^.+\\.(ts|tsx)$": [
          "ts-jest",
          {
            tsconfig: {
              jsx: "react-jsx",
            },
          },
        ],
      },
      moduleNameMapper: {
        "^@/(.*)$": "<rootDir>/src/$1",
      },
      setupFilesAfterEnv: ["<rootDir>/src/setupTests.ts"],
    },
    // Backend tests (API routes, utilities, server functions)
    {
      displayName: "server",
      testEnvironment: "node",
      testMatch: [
        "<rootDir>/src/app/api/**/*.test.{ts,tsx}",
        "<rootDir>/src/utils/**/*.test.{ts,tsx}",
        "<rootDir>/src/lib/**/*.test.{ts,tsx}",
        "<rootDir>/src/**/*.server.test.{ts,tsx}",
      ],
      moduleFileExtensions: ["ts", "tsx", "js", "jsx", "json", "node"],
      transform: {
        "^.+\\.(ts|tsx)$": ["ts-jest", { tsconfig: "tsconfig.json" }],
      },
      moduleNameMapper: {
        "^@/(.*)$": "<rootDir>/src/$1",
      },
      setupFilesAfterEnv: ["<rootDir>/src/setupServerTests.ts"],
    },
  ],
  // Global settings
  collectCoverageFrom: [
    "src/**/*.{ts,tsx}",
    "!src/**/*.d.ts",
    "!src/generated/**",
  ],
};
