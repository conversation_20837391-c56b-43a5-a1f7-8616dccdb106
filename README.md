This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [<PERSON>eist](https://vercel.com/font), a new font family for Vercel.

## Testing

This project uses Jest with React Testing Library for comprehensive testing. The test suite is configured with separate projects for client-side and server-side code.

### Available Test Commands

**Run all tests (client + server):**

```bash
npm test
```

**Run only frontend/client tests:**

```bash
npm run test:client
```

_Includes: React components, pages, layouts, hooks, and client-side code_

**Run only backend/server tests:**

```bash
npm run test:server
```

_Includes: API routes, utilities, server functions, and server-side code_

**Run only component tests:**

```bash
npm run test:components
```

_Includes: All tests in the `src/components` directory_

### Test Options

You can add additional flags to customize test execution:

```bash
# Run tests in watch mode (reruns when files change)
npm run test:client -- --watch

# Run tests with coverage report
npm run test:client -- --coverage

# Run a specific test file
npm run test:client -- Button.test.tsx

# Run tests matching a pattern
npm run test:client -- --testNamePattern="Button Component"
```

### Test Structure

- **Client Tests** (`jsdom` environment): Located in `src/components/`, `src/app/pages/`, `src/hooks/`
- **Server Tests** (`node` environment): Located in `src/app/api/`, `src/utils/`, `src/lib/`
- **Setup Files**: `src/setupTests.ts` (client), `src/setupServerTests.ts` (server)

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
