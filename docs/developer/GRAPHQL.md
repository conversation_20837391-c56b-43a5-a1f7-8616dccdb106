# GraphQL Documentation

- [GraphQL API][#graphql-api]
- [Authentication & Refresh Token](#Authentication)

## Graphql API

Because our graphQL API proxies the Supabase API, you can find the official [documentation here](https://supabase.com/docs/guides/graphql)

**Route**

```
http://example.com/api/graphql
```

---

## Authentication & Refresh Token

https://documenter.getpostman.com/view/3688534/2sB2x9kB9c
